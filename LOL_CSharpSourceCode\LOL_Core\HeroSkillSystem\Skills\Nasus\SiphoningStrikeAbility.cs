using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Localization;
using TaleWorlds.Library;
using TOR_Core.AbilitySystem;
using LOL_Core.HeroSkillSystem.Core;
using LOL_Core.Extensions;

namespace LOL_Core.HeroSkillSystem.Skills.Nasus
{
    public class SiphoningStrikeAbility : HeroAbility
    {
        private const string SKILL_ID = "nasus_siphoning_strike";
        private const float MELEE_RANGE = 2.0f;

        public SiphoningStrikeAbility(HeroAbilityTemplate template) : base(template)
        {
        }

        public override bool CanCast(Agent casterAgent, out TextObject failureReason)
        {
            if (!base.CanCast(casterAgent, out failureReason))
            {
                return false;
            }

            if (!casterAgent.IsMainAgent)
            {
                failureReason = new TextObject("Only player can use this skill");
                return false;
            }

            if (!HasValidTarget(casterAgent))
            {
                failureReason = new TextObject("No enemy in range");
                return false;
            }

            if (casterAgent.GetWieldedItemIndex(Agent.HandIndex.MainHand) == EquipmentIndex.None)
            {
                failureReason = new TextObject("Requires a weapon");
                return false;
            }

            return true;
        }

        private bool HasValidTarget(Agent casterAgent)
        {
            var nearbyAgents = Mission.Current.GetNearbyAgents(
                casterAgent.Position.AsVec2, 
                MELEE_RANGE, 
                new TaleWorlds.Library.MBList<Agent>());

            foreach (var agent in nearbyAgents)
            {
                if (IsValidTarget(agent, casterAgent))
                {
                    return true;
                }
            }
            return false;
        }

        private bool IsValidTarget(Agent target, Agent caster)
        {
            return target != null &&
                   target != caster &&
                   target.IsActive() &&
                   target.Health > 0 &&
                   target.Team != caster.Team &&
                   !target.IsMainAgent;
        }

        public override void ActivateAbility(Agent casterAgent)
        {
            base.ActivateAbility(casterAgent);

            var targetAgent = FindNearestTarget(casterAgent);
            if (targetAgent != null)
            {
                PerformSiphoningStrike(casterAgent, targetAgent);
            }
        }

        private Agent FindNearestTarget(Agent casterAgent)
        {
            var nearbyAgents = Mission.Current.GetNearbyAgents(
                casterAgent.Position.AsVec2,
                MELEE_RANGE,
                new TaleWorlds.Library.MBList<Agent>());

            Agent closestTarget = null;
            float closestDistance = float.MaxValue;

            foreach (var agent in nearbyAgents)
            {
                if (IsValidTarget(agent, casterAgent))
                {
                    float distance = casterAgent.Position.Distance(agent.Position);
                    if (distance < closestDistance)
                    {
                        closestDistance = distance;
                        closestTarget = agent;
                    }
                }
            }

            return closestTarget;
        }

        private void PerformSiphoningStrike(Agent casterAgent, Agent targetAgent)
        {
            var weaponData = casterAgent.WieldedWeapon;
            var currentDamage = GetCurrentDamage();
            
            var blow = CreateSiphoningStrikeBlow(casterAgent, targetAgent, currentDamage, weaponData);
            
            // 创建攻击碰撞数据
            var collisionData = new AttackCollisionData();
            targetAgent.RegisterBlow(blow, collisionData);
            
            PlaySiphoningStrikeEffects(casterAgent, targetAgent);

            if (targetAgent.Health <= 0)
            {
                OnTargetKilled(casterAgent, targetAgent);
            }
        }

        private Blow CreateSiphoningStrikeBlow(Agent attacker, Agent victim, float damage, MissionWeapon weapon)
        {
            var blow = new Blow(attacker.Index);
            blow.DamageType = TaleWorlds.Core.DamageTypes.Pierce;
            blow.BoneIndex = victim.Monster.HeadLookDirectionBoneIndex;
            blow.GlobalPosition = victim.Position;
            blow.GlobalPosition = new Vec3(blow.GlobalPosition.x, blow.GlobalPosition.y, blow.GlobalPosition.z + victim.GetEyeGlobalHeight());
            blow.BaseMagnitude = (int)damage;
            blow.WeaponRecord.AffectorWeaponSlotOrMissileIndex = (int)attacker.GetWieldedItemIndex(Agent.HandIndex.MainHand);
            blow.WeaponRecord.WeaponFlags = weapon.Item?.WeaponComponent?.PrimaryWeapon.WeaponFlags ?? (WeaponFlags)0;
            blow.AttackType = AgentAttackType.Standard;
            blow.VictimBodyPart = BoneBodyPartType.Head;

            return blow;
        }

        private void PlaySiphoningStrikeEffects(Agent caster, Agent target)
        {
            if (!string.IsNullOrEmpty(HeroTemplate.SoundEffectToPlay))
            {
                // 播放音效 - 使用简化的方法
                Mission.Current.MakeSound(
                    0, // 使用默认音效ID
                    caster.Position,
                    false,
                    true,
                    caster.Index,
                    -1);
            }

            caster.MakeVoice(SkinVoiceManager.VoiceType.Grunt, SkinVoiceManager.CombatVoiceNetworkPredictionType.NoPrediction);
            
            if (HeroTemplate.HasLight || !string.IsNullOrEmpty(HeroTemplate.ParticleEffectPrefab))
            {
            }
        }

        private void OnTargetKilled(Agent killer, Agent victim)
        {
            ProcessKill(victim, killer);
            
            Mission.Current.GetMissionBehavior<LOL_Core.MissionLogics.HeroSkillMissionLogic>()
                ?.OnHeroSkillKill(this, killer, victim);
        }

        public override string GetSkillDescription()
        {
            var baseDescription = $"内瑟斯挥舞武器进行一次强力攻击。";
            baseDescription += $"\n基础伤害: {HeroTemplate.BaseDamage}";
            baseDescription += $"\n冷却时间: {HeroTemplate.CoolDown}秒";
            
            if (HeroTemplate.CanGrow)
            {
                baseDescription += $"\n\n被动效果: 击败敌人永久增加 {HeroTemplate.GrowthPerKill} 点伤害";
                baseDescription += $"\n当前层数: {GrowthData.CurrentStacks}";
                baseDescription += $"\n当前总伤害: {GetCurrentDamage():F0}";
                baseDescription += $"\n总击杀数: {GrowthData.TotalKills}";
            }

            return baseDescription;
        }
    }
}
