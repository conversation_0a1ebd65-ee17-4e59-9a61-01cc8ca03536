﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{80942161-02FB-4024-A1C2-22EF33DC8D1A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>TOR_Core</RootNamespace>
    <AssemblyName>TOR_Core</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <LangVersion>12</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\bin\Win64_Shipping_Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE;_RGL_KEEP_ASSERTS</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <DocumentationFile>
    </DocumentationFile>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\bin\Win64_Shipping_Client\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <StartAction>Program</StartAction>
    <StartProgram>..\..\..\bin\Win64_Shipping_Client\Bannerlord.exe</StartProgram>
    <StartArguments>/singleplayer _MODULES_%2aNative%2aSandBoxCore%2aSandBox%2aStoryMode%2aCustomBattle%2aTOR_Armory%2aTOR_Environment%2aTOR_Core%2a_MODULES_</StartArguments>
    <StartWorkingDirectory>..\..\..\bin\Win64_Shipping_Client</StartWorkingDirectory>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\bin\Win64_Shipping_Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>12.0</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>..\bin\Win64_Shipping_Client\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>12.0</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ink-engine-runtime, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\ink-engine-runtime.dll</HintPath>
    </Reference>
    <Reference Include="ink_compiler, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>lib\ink_compiler.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.AchievementSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.AchievementSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.CampaignSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.CampaignSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.CampaignSystem.ViewModelCollection.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.CampaignSystem.ViewModelCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Core.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Core.ViewModelCollection.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Core.ViewModelCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.DotNet.AutoGenerated.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.DotNet.AutoGenerated.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.DotNet.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.DotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Engine.AutoGenerated.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Engine.AutoGenerated.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Engine.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Engine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Engine.GauntletUI.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Engine.GauntletUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.Data.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.ExtraWidgets.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.ExtraWidgets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.PrefabSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.PrefabSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.TooltipExtensions.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.TooltipExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.InputSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Library.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Library.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.LinQuick.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.LinQuick.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Localization.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Localization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.ModuleManager.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.ModuleManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.AutoGenerated.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.AutoGenerated.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Diamond.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Diamond.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.GauntletUI.Widgets.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.GauntletUI.Widgets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Helpers.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Helpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Launcher.Steam.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Launcher.Steam.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Multiplayer">
      <HintPath>..\..\Multiplayer\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Multiplayer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.ViewModelCollection.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.ViewModelCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.NavigationSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.NavigationSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Network.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Network.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.ObjectSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.ObjectSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.Epic.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.Epic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.GOG.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.GOG.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.Steam.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.Steam.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.PlayerServices.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.PlayerServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.PSAI.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.PSAI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.SaveSystem.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.SaveSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.ScreenSystem">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.ScreenSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.Starter.Library.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.Starter.Library.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.TwoDimension.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.TwoDimension.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.TwoDimension.Standalone.dll">
      <HintPath>..\..\..\bin\Win64_Shipping_Client\TaleWorlds.TwoDimension.Standalone.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.GauntletUI.dll">
      <HintPath>..\..\..\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.GauntletUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Platform.PC.dll">
      <HintPath>..\..\..\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Platform.PC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.View.dll">
      <HintPath>..\..\..\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.View.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SandBox.dll">
      <HintPath>..\..\..\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SandBox.GauntletUI.dll">
      <HintPath>..\..\..\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.GauntletUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SandBox.View.dll">
      <HintPath>..\..\..\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.View.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="SandBox.ViewModelCollection.dll">
      <HintPath>..\..\..\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.ViewModelCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StoryMode.dll">
      <HintPath>..\..\..\Modules\StoryMode\bin\Win64_Shipping_Client\StoryMode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StoryMode.GauntletUI.AutoGenerated.dll">
      <HintPath>..\..\..\Modules\StoryMode\bin\Win64_Shipping_Client\StoryMode.GauntletUI.AutoGenerated.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StoryMode.GauntletUI.dll">
      <HintPath>..\..\..\Modules\StoryMode\bin\Win64_Shipping_Client\StoryMode.GauntletUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StoryMode.View.dll">
      <HintPath>..\..\..\Modules\StoryMode\bin\Win64_Shipping_Client\StoryMode.View.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="StoryMode.ViewModelCollection.dll">
      <HintPath>..\..\..\Modules\StoryMode\bin\Win64_Shipping_Client\StoryMode.ViewModelCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.CustomBattle.dll">
      <HintPath>..\..\..\Modules\CustomBattle\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.CustomBattle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Numerics" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AbilitySystem\Ability.cs" />
    <Compile Include="AbilitySystem\AbilityComponent.cs" />
    <Compile Include="AbilitySystem\AbilityFactory.cs" />
    <Compile Include="AbilitySystem\AbilityHUDMissionView.cs" />
    <Compile Include="AbilitySystem\AbilityHUD_VM.cs" />
    <Compile Include="AbilitySystem\AbilityManagerMissionLogic.cs" />
    <Compile Include="AbilitySystem\AbilityRadialSelectionItemWidget.cs" />
    <Compile Include="AbilitySystem\AbilityRadialSelectionItem_VM.cs" />
    <Compile Include="AbilitySystem\AbilityRadialSelection_VM.cs" />
    <Compile Include="AbilitySystem\AbilityTemplate.cs" />
    <Compile Include="AbilitySystem\AbilityType.cs" />
    <Compile Include="AbilitySystem\CareerAbility.cs" />
    <Compile Include="AbilitySystem\CrossHairs\AbilityCrosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\Crosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\CrosshairType.cs" />
    <Compile Include="AbilitySystem\CrossHairs\ICrosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\MissileCrosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\Pointer.cs" />
    <Compile Include="AbilitySystem\CrossHairs\ProjectileCrosshair_VM.cs" />
    <Compile Include="AbilitySystem\CrossHairs\SelfCrosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\SingleTargetCrosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\TargetedAOECrosshair.cs" />
    <Compile Include="AbilitySystem\CrossHairs\WindCrosshair.cs" />
    <Compile Include="AbilitySystem\ItemBoundAbility.cs" />
    <Compile Include="AbilitySystem\Prayer.cs" />
    <Compile Include="AbilitySystem\Scripts\AbilityScript.cs" />
    <Compile Include="AbilitySystem\Scripts\AccusationScript.cs" />
    <Compile Include="AbilitySystem\Scripts\ArcaneConduit.cs" />
    <Compile Include="AbilitySystem\Scripts\ArrowOfKurnousScript.cs" />
    <Compile Include="AbilitySystem\Scripts\ArtilleryPlacementScript.cs" />
    <Compile Include="AbilitySystem\Scripts\AugmentScript.cs" />
    <Compile Include="AbilitySystem\Scripts\AxeOfUlricScript.cs" />
    <Compile Include="AbilitySystem\Scripts\BlastScript.cs" />
    <Compile Include="AbilitySystem\Scripts\BombardmentScript.cs" />
    <Compile Include="AbilitySystem\Scripts\CareerAbilityMissleScript.cs" />
    <Compile Include="AbilitySystem\Scripts\CareerAbilityScript.cs" />
    <Compile Include="AbilitySystem\Scripts\MindControlScript.cs" />
    <Compile Include="AbilitySystem\Scripts\HealScript.cs" />
    <Compile Include="AbilitySystem\Scripts\HexScript.cs" />
    <Compile Include="AbilitySystem\Scripts\KnightlyChargeScript.cs" />
    <Compile Include="AbilitySystem\Scripts\MissileScript.cs" />
    <Compile Include="AbilitySystem\Scripts\BlastOfAgonyScript.cs" />
    <Compile Include="AbilitySystem\Scripts\ProjectileScript.cs" />
    <Compile Include="AbilitySystem\Scripts\RedFuryScript.cs" />
    <Compile Include="AbilitySystem\Scripts\ShadowStepScript.cs" />
    <Compile Include="AbilitySystem\Scripts\SummonChampionScript.cs" />
    <Compile Include="AbilitySystem\Scripts\SummoningScript.cs" />
    <Compile Include="AbilitySystem\Scripts\TeleportScript.cs" />
    <Compile Include="AbilitySystem\Scripts\TimeWarpScript.cs" />
    <Compile Include="AbilitySystem\Scripts\VortexScript.cs" />
    <Compile Include="AbilitySystem\Scripts\WindScript.cs" />
    <Compile Include="AbilitySystem\Scripts\WrathOfTheWoodScript.cs" />
    <Compile Include="AbilitySystem\SeekerController.cs" />
    <Compile Include="AbilitySystem\SeekerParameters.cs" />
    <Compile Include="AbilitySystem\CareerAbilityHUD_VM.cs" />
    <Compile Include="AbilitySystem\Spells\Prayers\BattlePrayerBookState.cs" />
    <Compile Include="AbilitySystem\Spells\Prayers\BattlePrayerScreen.cs" />
    <Compile Include="AbilitySystem\Spells\Prayers\BattlePrayersVM.cs" />
    <Compile Include="AbilitySystem\Spells\Prayers\PrayerItemVM.cs" />
    <Compile Include="AbilitySystem\Spells\Prayers\PrayerLoreObjectVM.cs" />
    <Compile Include="AbilitySystem\Spells\Spell.cs" />
    <Compile Include="AbilitySystem\Spells\LoreObject.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\AbilityItemVM.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\LoreObjectVM.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\SpellBookMapIconVM.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\SpellBookScreen.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\SpellBookState.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\SpellBookVM.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\SpellItemVM.cs" />
    <Compile Include="AbilitySystem\Spells\SpellBook\StatItemVM.cs" />
    <Compile Include="AbilitySystem\Spells\SpellCastingLevel.cs" />
    <Compile Include="AbilitySystem\SummonedAgentOrigin.cs" />
    <Compile Include="Audio\CachedSound.cs" />
    <Compile Include="Audio\CachedSoundInstance.cs" />
    <Compile Include="Audio\TORAudioAmbientSoundEmitter.cs" />
    <Compile Include="Audio\TORAudioEngine.cs" />
    <Compile Include="Audio\TORAudioManager.cs" />
    <Compile Include="Audio\TORMixingSampleProvider.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehaviorConfiguration.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\AbstractAgentCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\AoEAdjacentCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\AoEDirectionalCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\AoETargetedCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\ArtilleryPlacementCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\MissileCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\PreserveWindsCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\SelectMultiTargetCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\SelectSingleTargetCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentCastingBehavior\SummoningCastingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentTacticalBehavior\AbstractAgentTacticalBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentTacticalBehavior\AoEAdjacentTacticalBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentTacticalBehavior\AoEDirectionalTacticalBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\AgentTacticalBehavior\KeepSafeTacticalBehavior.cs" />
    <Compile Include="BattleMechanics\AI\ArtilleryAI\FieldSiegeWeaponAI.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\Components\WizardAIComponent.cs" />
    <Compile Include="BattleMechanics\AI\CivilianMissionAI\TORAlarmedBehaviorGroup.cs" />
    <Compile Include="BattleMechanics\AI\CivilianMissionAI\TORDailyBehaviorGroup.cs" />
    <Compile Include="BattleMechanics\AI\CivilianMissionAI\TORFightBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CivilianMissionAI\TORWalkingBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\SupportMissionLogic\QuerySystemExtensions.cs" />
    <Compile Include="BattleMechanics\AI\CastingAI\SupportMissionLogic\QuerySystemExtensionsMissionLogic.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\Axis.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\BehaviorOption.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\CommonAIFunctions.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\DecisionManager.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\IAgentBehavior.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\ScoringFunctions.cs" />
    <Compile Include="BattleMechanics\AI\CommonAIFunctions\Target.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\FormationBehavior\TORBehaviorBase.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\FormationBehavior\TORBehaviorProtectArtillery.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\FormationBehavior\TORBehaviorDefend.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\TeamBehavior\Tactics\TORTacticPositionalArtillery.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\FormationBehavior\TORFormationClass.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\TORMissionCombatantsLogic.cs" />
    <Compile Include="BattleMechanics\AI\TeamAI\TeamBehavior\TORTeamAIGeneral.cs" />
    <Compile Include="BattleMechanics\Artillery\ArtilleryRangedSiegeWeapon.cs" />
    <Compile Include="BattleMechanics\Artillery\ArtilleryStandingPoint.cs" />
    <Compile Include="BattleMechanics\Artillery\Ballistics.cs" />
    <Compile Include="BattleMechanics\Artillery\BaseFieldSiegeWeapon.cs" />
    <Compile Include="BattleMechanics\Artillery\CannonBallPile.cs" />
    <Compile Include="BattleMechanics\Artillery\FieldTrebuchet.cs" />
    <Compile Include="BattleMechanics\Atmosphere\ForceAtmosphereMissionLogic.cs" />
    <Compile Include="BattleMechanics\Banners\CustomBannerManager.cs" />
    <Compile Include="BattleMechanics\Banners\CustomBannerMissionLogic.cs" />
    <Compile Include="BattleMechanics\CareerPerkMissionBehavior.cs" />
    <Compile Include="BattleMechanics\CustomArenaModes\ArcheryContestAgentController.cs" />
    <Compile Include="BattleMechanics\CustomArenaModes\ArcheryContestTournamentGame.cs" />
    <Compile Include="BattleMechanics\CustomCrosshairMissionBehavior.cs" />
    <Compile Include="BattleMechanics\DamageSystem\DamageType.cs" />
    <Compile Include="BattleMechanics\Dismemberment\DismembermentMissionLogic.cs" />
    <Compile Include="BattleMechanics\DualWield\DualWieldAgentComponent.cs" />
    <Compile Include="BattleMechanics\DualWield\DualWieldMissionLogic.cs" />
    <Compile Include="BattleMechanics\Firearms\FirearmsMissionLogic.cs" />
    <Compile Include="BattleMechanics\CustomArenaModes\JoustLaneEndVolumeBox.cs" />
    <Compile Include="BattleMechanics\CustomArenaModes\CustomTournamentBehaviors.cs" />
    <Compile Include="BattleMechanics\CustomArenaModes\JoustTournamentGame.cs" />
    <Compile Include="BattleMechanics\Morale\AgentVoiceComponent.cs" />
    <Compile Include="BattleMechanics\Morale\BattleShoutsMissionLogic.cs" />
    <Compile Include="BattleMechanics\Morale\UndeadMoraleAgentComponent.cs" />
    <Compile Include="BattleMechanics\Morale\MoraleMissionLogic.cs" />
    <Compile Include="BattleMechanics\SFX\TORFaceEnemy.cs" />
    <Compile Include="BattleMechanics\SFX\TORLightDampener.cs" />
    <Compile Include="BattleMechanics\SFX\TORSimpleObjectAnimator.cs" />
    <Compile Include="BattleMechanics\SFX\TORSpinner.cs" />
    <Compile Include="BattleMechanics\SFX\PlayerFlyableObjectScript.cs" />
    <Compile Include="BattleMechanics\SniperScope\SniperScope.cs" />
    <Compile Include="BattleMechanics\StatusEffect\StatusEffect.cs" />
    <Compile Include="BattleMechanics\StatusEffect\StatusEffectComponent.cs" />
    <Compile Include="BattleMechanics\StatusEffect\StatusEffectManager.cs" />
    <Compile Include="BattleMechanics\StatusEffect\StatusEffectMissionLogic.cs" />
    <Compile Include="BattleMechanics\StatusEffect\StatusEffectTemplate.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\AnimationTrigger.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\AnimationTriggerManager.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\AnimationTriggerMissionLogic.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\Scripts\DynamicItemTraitScripts.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\Scripts\ITriggeredScript.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\Scripts\KnockDownScript.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\Scripts\PrefabSpawnerScript.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\Scripts\SummonScript.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\Scripts\TraitHelper.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\TriggeredEffect.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\TriggeredEffectManager.cs" />
    <Compile Include="BattleMechanics\TriggeredEffect\TriggeredEffectTemplate.cs" />
    <Compile Include="CampaignMechanics\AICompanions\TORAICompanionCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Assimilation\AssimilationCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Assimilation\RaceFixCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\BountyMaster\BountyMasterCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\CampaignEventHelpers.cs" />
    <Compile Include="CampaignMechanics\Careers\CareerButtonDialogs.cs" />
    <Compile Include="CampaignMechanics\Careers\CareerDialogOptionsCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Careers\GrailDamselEnvoyOfTheLadyPerkDialog.cs" />
    <Compile Include="CampaignMechanics\Careers\TORCareerPerkCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Chaos\ChaosCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\CharacterCreation\FaceGenHelper.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\CareerSwitchCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\BloodKissSceneNotificationItem.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\ConversationTags\AsraiTag.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\ConversationTags\BloodDragonTag.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\ConversationTags\CommonTags.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\ConversationTags\EonirTag.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\ConversationTags\MousillonTag.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\ConversationTags\VampireTags.cs" />
    <Compile Include="CampaignMechanics\CustomDialogs\CustomDialogCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\CustomResources\ChivalryHelper.cs" />
    <Compile Include="CampaignMechanics\CustomResources\CustomResource.cs" />
    <Compile Include="CampaignMechanics\CustomResources\CustomResourceManager.cs" />
    <Compile Include="CampaignMechanics\CustomResources\FavorHelper.cs" />
    <Compile Include="CampaignMechanics\CustomResources\ForestHarmonyHelper.cs" />
    <Compile Include="CampaignMechanics\CustomResources\PartyVMExtension.cs" />
    <Compile Include="CampaignMechanics\CustomEvents\CustomEvent.cs" />
    <Compile Include="CampaignMechanics\CustomEvents\CustomEventsCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Diplomacy\TORKingdomDecisionsCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Invasions\InvasionCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Invasions\InvasionPartyComponent.cs" />
    <Compile Include="CampaignMechanics\MasterEngineer\MasterEngineerTownBehavior.cs" />
    <Compile Include="CampaignMechanics\PrestigeNoble\EonirFavorEnvoyTownBehavior.cs" />
    <Compile Include="CampaignMechanics\PrestigeNoble\PrestigeNobleTownBehavior.cs" />
    <Compile Include="CampaignMechanics\RaidingParties\RaidingPartyComponent.cs" />
    <Compile Include="CampaignMechanics\CharacterCreation\CharacterCreationOption.cs" />
    <Compile Include="CampaignMechanics\CharacterCreation\TORCharacterCreationContent.cs" />
    <Compile Include="CampaignMechanics\RaidingParties\IRaidingParty.cs" />
    <Compile Include="CampaignMechanics\RaidingParties\RaidingPartyCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\RaiseDead\GraveyardNightWatchPartyComponent.cs" />
    <Compile Include="CampaignMechanics\RaiseDead\PostBattleCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\RaiseDead\RaiseDeadInTownBehavior.cs" />
    <Compile Include="CampaignMechanics\RegimentsOfRenown\RORCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\RegimentsOfRenown\RORManager.cs" />
    <Compile Include="CampaignMechanics\RegimentsOfRenown\ToRSettlementNameplateVM.cs" />
    <Compile Include="CampaignMechanics\RegimentsOfRenown\RORSettlementTemplate.cs" />
    <Compile Include="CampaignMechanics\Religion\EncyclopediaReligionObjectVM.cs" />
    <Compile Include="CampaignMechanics\Religion\ReligionCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\Religion\ReligionEncyclopediaPage.cs" />
    <Compile Include="CampaignMechanics\Religion\ReligionObject.cs" />
    <Compile Include="CampaignMechanics\Religion\ReligionObjectHelper.cs" />
    <Compile Include="CampaignMechanics\ServeAsAHireling\ServeAsAHirelingActivities.cs" />
    <Compile Include="CampaignMechanics\ServeAsAHireling\ServeAsAHirelingHelpers.cs" />
    <Compile Include="CampaignMechanics\SkillBooks\TORSkillBookCampaignBehavior.cs" />
    <Compile Include="BattleMechanics\TORBattleAgentLogic.cs" />
    <Compile Include="CampaignMechanics\SpellTrainers\SpellTrainerInTownBehavior.cs" />
    <Compile Include="CampaignMechanics\TORCaptivityCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\CustomSettlementMenus\OakOfAgesMenuLogic.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\CustomSettlementMenus\RaidingSiteMenuLogic.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\CustomSettlementMenus\CursedSiteMenuLogic.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\CustomSettlementMenus\ShrineMenuLogic.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\CustomSettlementMenus\TORBaseSettlementMenuLogic.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\TORBaseSettlementComponent.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\TORCustomSettlementCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\TORCustomSettlement\TORSettlementComponents.cs" />
    <Compile Include="CampaignMechanics\TORFactionDiscontinuationCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\TORMapBarSpriteWidget.cs" />
    <Compile Include="CampaignMechanics\TORMapInfoVMExtension.cs" />
    <Compile Include="CampaignMechanics\TORPartyUpgraderCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\TORAIRecruitmentCampaignBehavior.cs" />
    <Compile Include="CampaignMechanics\TORRecruitmentHelpers.cs" />
    <Compile Include="CampaignMechanics\TORSpecialSettlementBehavior.cs" />
    <Compile Include="CampaignMechanics\TORWanderersCampaignBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerAbilityChargeSupplier.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\BlackGrailKnightCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\CareerButtonBehaviorBase.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\CareerButtons.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\GrailKnightCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\ImperialMagisterCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\MercenaryCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerAbilityEffectVM.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\NecrarchCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\SpecialbuttonHandler.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\WaywatcherCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerButton\WitchHunterCareerButtonBehavior.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerChoiceGroupObject.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerChoiceGroupObjectVM.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerChoiceObject.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerChoiceObjectVM.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerHelper.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerObject.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerObjectVM.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerScreen.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerScreenGameState.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CareerScreenVM.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\CharacterDeveloperVMExtension.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\BlackGrailKnightCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\BloodKnightCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\CareerChoicesHelper.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\GrailKnightCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\GrailDamselCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\GreyLordCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\ImperialMagisterCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\MercenaryCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\NecrarchCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\NecromancerCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\SpellsingerCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\TORCareerChoicesBase.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\VampireCountCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\WarriorPriestCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\WarriorPriestUlricCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\WaywatcherCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\CareerSystem\Choices\WitchHunterCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\TORAttributes.cs" />
    <Compile Include="CharacterDevelopment\TORCareerChoiceGroups.cs" />
    <Compile Include="CharacterDevelopment\TORCareerChoices.cs" />
    <Compile Include="CharacterDevelopment\TORCareers.cs" />
    <Compile Include="CharacterDevelopment\TORCharacterTraits.cs" />
    <Compile Include="CharacterDevelopment\TORPerkHandlerCampaignBehavior.cs" />
    <Compile Include="CharacterDevelopment\TORPerks.cs" />
    <Compile Include="CharacterDevelopment\TORSkillEffects.cs" />
    <Compile Include="CharacterDevelopment\TORSkills.cs" />
    <Compile Include="Extensions\AgentDrivenPropertiesExtensions.cs" />
    <Compile Include="Extensions\AgentExtensions.cs" />
    <Compile Include="Extensions\BannerExtensions.cs" />
    <Compile Include="Extensions\CharacterObjectExtensions.cs" />
    <Compile Include="Extensions\ClanExtensions.cs" />
    <Compile Include="Extensions\CollectionExtensions.cs" />
    <Compile Include="Extensions\DebugMethods.cs" />
    <Compile Include="Extensions\ExtendedInfoSystem\CharacterExtendedInfo.cs" />
    <Compile Include="Extensions\ExtendedInfoSystem\ExtendedInfoManager.cs" />
    <Compile Include="Extensions\ExtendedInfoSystem\ExtendedInfoMissionLogic.cs" />
    <Compile Include="Extensions\ExtendedInfoSystem\HeroExtendedInfo.cs" />
    <Compile Include="Extensions\ExtendedInfoSystem\MobilePartyExtendedInfo.cs" />
    <Compile Include="Extensions\FactionExtensions.cs" />
    <Compile Include="Extensions\FormationExtensions.cs" />
    <Compile Include="Extensions\GameModelsExtensions.cs" />
    <Compile Include="Extensions\HeroExtensions.cs" />
    <Compile Include="Extensions\ItemObjectExtensions.cs" />
    <Compile Include="Extensions\KingdomExtension.cs" />
    <Compile Include="Extensions\MissionExtensions.cs" />
    <Compile Include="Extensions\MobilePartyExtensions.cs" />
    <Compile Include="Extensions\SettlementExtensions.cs" />
    <Compile Include="Extensions\SkillExtensions.cs" />
    <Compile Include="Extensions\TeamExtensions.cs" />
    <Compile Include="Extensions\UI\BaseViewModelExtension.cs" />
    <Compile Include="Extensions\UI\HeroEncyclopediaVMExtension.cs" />
    <Compile Include="Extensions\UI\MissionConversationVMExtension.cs" />
    <Compile Include="Extensions\UI\PartyCharacterVMExtension.cs" />
    <Compile Include="Extensions\UI\PartyNameplateItemVMExtension.cs" />
    <Compile Include="Extensions\UI\UnitEncyclopediaVMExtension.cs" />
    <Compile Include="Extensions\UI\ViewModelExtensionAttribute.cs" />
    <Compile Include="Extensions\UI\ViewModelExtensionManager.cs" />
    <Compile Include="CampaignMechanics\ServeAsAHireling\ServeAsAHirelingCampaignBehavior.cs" />
    <Compile Include="Extensions\ViewModelExtensions.cs" />
    <Compile Include="GameManagers\TORKeyInputManager.cs" />
    <Compile Include="GameManagers\TORGameKeyContext.cs" />
    <Compile Include="GameManagers\TORShaderGameManager.cs" />
    <Compile Include="GameManagers\TORCampaignGameManager.cs" />
    <Compile Include="HarmonyPatches\AgentPatches.cs" />
    <Compile Include="HarmonyPatches\AIPatches.cs" />
    <Compile Include="HarmonyPatches\ArtilleryPatches.cs" />
    <Compile Include="HarmonyPatches\BaseGameDebugPatches.cs" />
    <Compile Include="HarmonyPatches\CaravanMasterPatch.cs" />
    <Compile Include="HarmonyPatches\CharacterCreationPatch.cs" />
    <Compile Include="HarmonyPatches\CharacterObjectPatches.cs" />
    <Compile Include="HarmonyPatches\ConversationPatches.cs" />
    <Compile Include="HarmonyPatches\CustomBattlePatches.cs" />
    <Compile Include="HarmonyPatches\CustomWorldMapPatch.cs" />
    <Compile Include="HarmonyPatches\DamagePatch.cs" />
    <Compile Include="HarmonyPatches\EncounterPatches.cs" />
    <Compile Include="HarmonyPatches\EncyclopediaConceptPatches.cs" />
    <Compile Include="HarmonyPatches\EncyclopediaPatches.cs" />
    <Compile Include="HarmonyPatches\FactionBannerPatches.cs" />
    <Compile Include="HarmonyPatches\GameKeyOptionsCategoryPatch.cs" />
    <Compile Include="HarmonyPatches\GameTextPatches.cs" />
    <Compile Include="HarmonyPatches\InkPatches.cs" />
    <Compile Include="HarmonyPatches\InventoryPatches.cs" />
    <Compile Include="HarmonyPatches\ItemPatches.cs" />
    <Compile Include="HarmonyPatches\LoadingScreenPatches.cs" />
    <Compile Include="HarmonyPatches\MainMenuOptionsPatches.cs" />
    <Compile Include="HarmonyPatches\MBMusicManagerPatches.cs" />
    <Compile Include="HarmonyPatches\MissionPatches.cs" />
    <Compile Include="HarmonyPatches\MobilePartyPatches.cs" />
    <Compile Include="HarmonyPatches\ModelPatches.cs" />
    <Compile Include="HarmonyPatches\MountCreationPatches.cs" />
    <Compile Include="HarmonyPatches\ObjectManagerPatches.cs" />
    <Compile Include="HarmonyPatches\CustomResourcePatches.cs" />
    <Compile Include="HarmonyPatches\PerkResetRelatedPatch.cs" />
    <Compile Include="HarmonyPatches\RotatingMainMenuScenePatch.cs" />
    <Compile Include="HarmonyPatches\SettlementPatches.cs" />
    <Compile Include="HarmonyPatches\TableauRenderPatches.cs" />
    <Compile Include="HarmonyPatches\ViewModelPatches.cs" />
    <Compile Include="HarmonyPatches\ViewModelRefreshPatch.cs" />
    <Compile Include="Ink\InkFakeMarketData.cs" />
    <Compile Include="Ink\InkStoryCampaignBehavior.cs" />
    <Compile Include="Ink\InkStory.cs" />
    <Compile Include="Ink\InkStoryChoiceVM.cs" />
    <Compile Include="Ink\InkStoryManager.cs" />
    <Compile Include="Ink\InkStoryVM.cs" />
    <Compile Include="Items\ExtendedItemObjectManager.cs" />
    <Compile Include="Items\ExtendedItemObjectProperties.cs" />
    <Compile Include="Items\ItemTrait.cs" />
    <Compile Include="Items\ItemTraitAgentComponent.cs" />
    <Compile Include="Items\IWeaponHitEffect.cs" />
    <Compile Include="Items\TorInventoryItemTupleWidget.cs" />
    <Compile Include="Items\TorItemMenuVM.cs" />
    <Compile Include="Items\TorImageIdentifierWidget.cs" />
    <Compile Include="Items\TorItemTraitVM.cs" />
    <Compile Include="Items\WeaponEffectMissionLogic.cs" />
    <Compile Include="Missions\ArcheryContestMissionController.cs" />
    <Compile Include="Missions\GraveyardFightMissionController.cs" />
    <Compile Include="Missions\InkDuelFightMissionController.cs" />
    <Compile Include="Missions\JoustFightMissionController.cs" />
    <Compile Include="Missions\QuestFightMissionController.cs" />
    <Compile Include="Models\CustomBattleModels\TORCustomBattleMoraleModel.cs" />
    <Compile Include="Models\TORAgentApplyDamageModel.cs" />
    <Compile Include="Models\TORAgentStatCalculateModel.cs" />
    <Compile Include="Models\TORAlleyModel.cs" />
    <Compile Include="Models\TORBanditDensityModel.cs" />
    <Compile Include="Models\TORBattleBannerBearersModel.cs" />
    <Compile Include="Models\TORBattleMoraleModel.cs" />
    <Compile Include="Models\TORBattleRewardModel.cs" />
    <Compile Include="Models\TORCharacterDevelopmentModel.cs" />
    <Compile Include="Models\TORCharacterStatsModel.cs" />
    <Compile Include="Models\TORClanFinanceModel.cs" />
    <Compile Include="Models\TORClanPoliticsModel.cs" />
    <Compile Include="Models\TORClanTierModel.cs" />
    <Compile Include="Models\TORCombatSimulationModel.cs" />
    <Compile Include="Models\TORCombatXpModel.cs" />
    <Compile Include="Models\TORCompanionHiringPriceCalculationModel.cs" />
    <Compile Include="Models\CustomBattleModels\TORCustomBattleAgentStatCalculateModel.cs" />
    <Compile Include="Models\TORCustomResourceModel.cs" />
    <Compile Include="Models\TORDamageParticleModel.cs" />
    <Compile Include="Models\TORDiplomacyModel.cs" />
    <Compile Include="Models\TOREncounterModel.cs" />
    <Compile Include="Models\TOREquipmentSelectionModel.cs" />
    <Compile Include="Models\TORFaithModel.cs" />
    <Compile Include="Models\TORSettlementDistanceModel.cs" />
    <Compile Include="Models\TORSettlementFoodModel.cs" />
    <Compile Include="Models\TORInventoryCapacityModel.cs" />
    <Compile Include="Models\TORKingdomDecisionPermissionModel.cs" />
    <Compile Include="Models\TORMapVisibilityModel.cs" />
    <Compile Include="Models\TORPartyMoraleModel.cs" />
    <Compile Include="Models\TORPartySpeedCalculatingModel.cs" />
    <Compile Include="Models\TOREncounterGameMenuModel.cs" />
    <Compile Include="Models\TORMapWeatherModel.cs" />
    <Compile Include="Models\TORMarriageModel.cs" />
    <Compile Include="Models\TORMobilePartyFoodConsumptionModel.cs" />
    <Compile Include="Models\TORPartyHealingModel.cs" />
    <Compile Include="Models\TORPartySizeModel.cs" />
    <Compile Include="Models\TORPartyTrainingModel.cs" />
    <Compile Include="Models\TORPartyTroopUpgradeModel.cs" />
    <Compile Include="Models\TORPartyWageModel.cs" />
    <Compile Include="Models\TORPersuasionModel.cs" />
    <Compile Include="Models\TORPrisonerRecruitmentCalculationModel.cs" />
    <Compile Include="Models\TORSettlementLoyaltyModel.cs" />
    <Compile Include="Models\TORRaidModel.cs" />
    <Compile Include="Models\TORSettlementMilitiaModel.cs" />
    <Compile Include="Models\TORAbilityModel.cs" />
    <Compile Include="Models\TORStrikeMagnitudeModel.cs" />
    <Compile Include="Models\TORTournamentModel.cs" />
    <Compile Include="Models\TORTroopSupplierModel.cs" />
    <Compile Include="Models\TORVoiceOverModel.cs" />
    <Compile Include="Models\TORVolunteerModel.cs" />
    <Compile Include="Quests\EngineerQuest.cs" />
    <Compile Include="Quests\HuntCultistsQuestCampaignBehavior.cs" />
    <Compile Include="Quests\PlaguedVillageQuestCampaignBehavior.cs" />
    <Compile Include="Quests\QuestPartyComponent.cs" />
    <Compile Include="Quests\SpecializeLoreQuest.cs" />
    <Compile Include="Quests\TORQuestHelper.cs" />
    <Compile Include="SaveGameSystem\SaveableTypeDefiners.cs" />
    <Compile Include="SubModule.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Utilities\DictionaryExtensions.cs" />
    <Compile Include="Utilities\StringExtensions.cs" />
    <Compile Include="Utilities\TORAnimationLogger.cs" />
    <Compile Include="Utilities\TORCommon.cs" />
    <Compile Include="Utilities\TORConfig.cs" />
    <Compile Include="Utilities\TORConsoleCommands.cs" />
    <Compile Include="Utilities\TORConstants.cs" />
    <Compile Include="Utilities\TORDamageDisplay.cs" />
    <Compile Include="Utilities\TOREntityRotator.cs" />
    <Compile Include="Utilities\TOREquipmentHelper.cs" />
    <Compile Include="Utilities\TORCampaignEvents.cs" />
    <Compile Include="Utilities\TORExtendedInfoHelper.cs" />
    <Compile Include="Utilities\TORGameMenuBackgroundSwitcher.cs" />
    <Compile Include="Utilities\TORGameStarterHelper.cs" />
    <Compile Include="Utilities\TORMassMaterialSwitcher.cs" />
    <Compile Include="Missions\TORMissionAgentHandler.cs" />
    <Compile Include="Utilities\TORMissionHelper.cs" />
    <Compile Include="Missions\TORMissionManager.cs" />
    <Compile Include="Utilities\TORParticleSystem.cs" />
    <Compile Include="Utilities\TORPaths.cs" />
    <Compile Include="Utilities\TORSpellBlowHelper.cs" />
    <Compile Include="Utilities\TORSummonHelper.cs" />
    <Compile Include="Utilities\TORTests.cs" />
    <Compile Include="Utilities\TORWorldMapScript.cs" />
    <Compile Include="Utilities\TORTextHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Lib.Harmony">
      <Version>2.3.3</Version>
    </PackageReference>
    <PackageReference Include="Microsoft.Win32.Registry">
      <Version>5.0.0</Version>
    </PackageReference>
    <PackageReference Include="NAudio">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NAudio.Asio">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NAudio.Core">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NAudio.Midi">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NAudio.Vorbis">
      <Version>1.5.0</Version>
    </PackageReference>
    <PackageReference Include="NAudio.Wasapi">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NAudio.WinForms">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NAudio.WinMM">
      <Version>2.2.1</Version>
    </PackageReference>
    <PackageReference Include="NLog">
      <Version>5.3.4</Version>
    </PackageReference>
    <PackageReference Include="NVorbis">
      <Version>0.10.5</Version>
    </PackageReference>
    <PackageReference Include="PolySharp">
      <Version>1.14.1</Version>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="System.Buffers">
      <Version>4.5.1</Version>
    </PackageReference>
    <PackageReference Include="System.Memory">
      <Version>4.5.5</Version>
    </PackageReference>
    <PackageReference Include="System.Numerics.Vectors">
      <Version>4.5.0</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe">
      <Version>6.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Security.AccessControl">
      <Version>6.0.1</Version>
    </PackageReference>
    <PackageReference Include="System.Security.Principal.Windows">
      <Version>5.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.ValueTuple">
      <Version>4.5.0</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>XCOPY "$(TargetDir)*" "$(TargetDir)..\Win64_Shipping_wEditor\" /S /Y /I</PostBuildEvent>
  </PropertyGroup>
</Project>