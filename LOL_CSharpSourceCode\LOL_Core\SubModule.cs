using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem;
using LOL_Core.CampaignBehaviors;
using LOL_Core.HeroSkillSystem.Core;

namespace LOL_Core
{
    public class SubModule : MBSubModuleBase
    {
        protected override void OnSubModuleLoad()
        {
            base.OnSubModuleLoad();
        }

        protected override void OnBeforeInitialModuleScreenSetAsRoot()
        {
            base.OnBeforeInitialModuleScreenSetAsRoot();
        }

        protected override void OnGameStart(Game game, IGameStarter gameStarterObject)
        {
            base.OnGameStart(game, gameStarterObject);

            if (gameStarterObject is CampaignGameStarter campaignStarter)
            {
                AddCampaignBehaviors(campaignStarter);
            }
        }

        private void AddCampaignBehaviors(CampaignGameStarter campaignStarter)
        {
            campaignStarter.AddBehavior(new HeroSkillCampaignBehavior());
            campaignStarter.AddBehavior(new HeroSkillDataBehavior());
        }

        public override void OnMissionBehaviorInitialize(Mission mission)
        {
            base.OnMissionBehaviorInitialize(mission);

            if (mission.Mode == MissionMode.Battle || 
                mission.Mode == MissionMode.Tournament ||
                mission.Mode == MissionMode.Duel)
            {
                mission.AddMissionBehavior(new LOL_Core.MissionLogics.HeroSkillMissionLogic());
            }
        }

        protected override void InitializeGameStarter(Game game, IGameStarter starterObject)
        {
            base.InitializeGameStarter(game, starterObject);
            HeroAbilityFactory.LoadTemplates();
        }
    }
}
