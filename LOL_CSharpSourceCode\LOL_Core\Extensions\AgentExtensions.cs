using TaleWorlds.MountAndBlade;
using LOL_Core.HeroSkillSystem.Core;
using System.Collections.Generic;
using TOR_Core.Extensions;

namespace LOL_Core.Extensions
{
    public static class AgentExtensions
    {
        public static HeroAbilityComponent GetHeroAbilityComponent(this Agent agent)
        {
            return agent.GetComponent<HeroAbilityComponent>();
        }

        public static bool HasHeroSkill(this Agent agent)
        {
            var component = agent.GetHeroAbilityComponent();
            return component != null && component.HasEquippedSkill();
        }

        public static HeroAbility GetEquippedHeroSkill(this Agent agent)
        {
            var component = agent.GetHeroAbilityComponent();
            return component?.GetEquippedSkill();
        }

        public static bool CanUseHeroSkill(this Agent agent)
        {
            if (!agent.HasHeroSkill()) return false;
            
            var skill = agent.GetEquippedHeroSkill();
            if (skill == null) return false;
            
            return skill.CanCast(agent, out _);
        }

        public static bool TryUseHeroSkill(this Agent agent)
        {
            if (!agent.CanUseHeroSkill()) return false;
            
            var skill = agent.GetEquippedHeroSkill();
            return skill != null && skill.TryCast(agent, out _);
        }

        public static List<string> GetSelectedHeroSkills(this Agent agent)
        {
            var hero = agent.GetHero();
            if (hero != null)
            {
                var equippedSkill = hero.GetEquippedSkillID();
                if (!string.IsNullOrEmpty(equippedSkill))
                {
                    return new List<string> { equippedSkill };
                }
            }
            return new List<string>();
        }

        public static float GetHeroSkillDamage(this Agent agent, string skillID)
        {
            var component = agent.GetHeroAbilityComponent();
            if (component != null)
            {
                var skill = component.GetSkillByID(skillID);
                if (skill != null)
                {
                    return skill.GetCurrentDamage();
                }
            }
            return 0f;
        }

        public static int GetHeroSkillStacks(this Agent agent, string skillID)
        {
            var hero = agent.GetHero();
            if (hero != null)
            {
                return hero.GetSkillStacks(skillID);
            }
            return 0;
        }

        public static bool IsUsingHeroSkill(this Agent agent)
        {
            var component = agent.GetHeroAbilityComponent();
            if (component != null)
            {
                var skill = component.GetEquippedSkill();
                return skill != null && skill.IsActive;
            }
            return false;
        }
    }
}
