<prefabs>
	<game_entity name="dwellers_below_tendril" old_prefab_name="" mobility="1">
		<transform position="0.000, 0.000, 0.000" rotation_euler="0.000, 0.000, 0.000"/>
		<skeleton skeleton_model="dwellers_below_skeleton">
			<components>
				<meta_mesh_component name="dwellers_below_tendril"/>
			</components>
			<bones>
				<bone frame="-0.076481, -0.026995, 0.996706, 0.000000, 0.991281, -0.109629, 0.073095, 0.000000, 0.107295, 0.993606, 0.035144, 0.000000, 0.000000, 0.000000, 0.000000, 1.000000"/>
				<bone frame="0.872293, -0.478338, 0.101476, 0.000000, -0.120655, -0.009441, 0.992650, 0.000000, -0.473864, -0.878125, -0.065950, 0.000000, 0.260927, -0.000000, 0.000000, 1.000000" _index_="1"/>
				<bone frame="0.925840, -0.372233, 0.065285, 0.000000, -0.054651, 0.039059, 0.997741, 0.000000, -0.373942, -0.927317, 0.015819, 0.000000, 0.345854, 0.000000, 0.000000, 1.000000" _index_="2"/>
				<bone frame="0.740181, -0.171418, -0.650190, 0.000000, 0.104600, 0.984541, -0.140490, 0.000000, 0.664222, 0.035978, 0.746669, 0.000000, 0.415073, 0.000000, 0.000000, 1.000000" _index_="3"/>
				<bone frame="0.951317, 0.220550, 0.215300, 0.000000, -0.301029, 0.814820, 0.495428, 0.000000, -0.066164, -0.536121, 0.841544, 0.000000, 0.332855, 0.000000, 0.000000, 1.000000" _index_="4"/>
				<bone frame="0.816701, 0.076310, 0.571994, 0.000000, -0.073193, 0.996911, -0.028493, 0.000000, -0.572401, -0.018596, 0.819763, 0.000000, 0.332855, 0.000000, -0.000000, 1.000000" _index_="5"/>
				<bone frame="0.599000, -0.430257, 0.675335, 0.000000, 0.578327, 0.815777, 0.006776, 0.000000, -0.553839, 0.386506, 0.737480, 0.000000, 0.334290, -0.000000, 0.000000, 1.000000" _index_="6"/>
				<bone frame="0.520980, -0.816967, 0.247275, 0.000000, 0.850040, 0.522894, -0.063356, 0.000000, -0.077539, 0.243201, 0.966872, 0.000000, 0.300000, 0.000000, 0.000000, 1.000000" _index_="7"/>
				<bone frame="0.886356, -0.045824, 0.460732, 0.000000, 0.218662, 0.918557, -0.329302, 0.000000, -0.408119, 0.392623, 0.824188, 0.000000, 0.219954, 0.084903, -0.023223, 1.000000" _index_="9"/>
				<bone frame="0.900840, -0.190897, 0.389930, 0.000000, 0.169641, 0.981515, 0.088602, 0.000000, -0.399636, -0.013668, 0.916572, 0.000000, 0.067170, -0.045328, -0.052348, 1.000000" _index_="11"/>
				<bone frame="0.476498, -0.637331, 0.605606, 0.000000, 0.622930, 0.730836, 0.278994, 0.000000, -0.620410, 0.244310, 0.745255, 0.000000, 0.204751, 0.000000, -0.000000, 1.000000" _index_="12"/>
				<bone frame="0.567990, -0.769228, 0.292702, 0.000000, 0.790501, 0.608876, 0.066170, 0.000000, -0.229119, 0.193797, 0.953911, 0.000000, 0.256841, -0.000000, 0.000000, 1.000000" _index_="13"/>
				<bone frame="0.855240, -0.496390, 0.148869, 0.000000, -0.007564, 0.275276, 0.961335, 0.000000, -0.518177, -0.823299, 0.231673, 0.000000, 0.100545, -0.053547, 0.085992, 1.000000" _index_="15"/>
				<bone frame="0.879106, 0.475463, 0.033291, 0.000000, -0.405700, 0.709796, 0.575845, 0.000000, 0.250163, -0.519735, 0.816881, 0.000000, 0.319892, 0.000000, -0.000000, 1.000000" _index_="16"/>
				<bone frame="0.713948, 0.407497, 0.569408, 0.000000, -0.695781, 0.321665, 0.642200, 0.000000, 0.078536, -0.854680, 0.513180, 0.000000, 0.354699, 0.068613, -0.097833, 1.000000" _index_="18"/>
				<bone frame="0.608481, 0.302566, 0.733624, 0.000000, -0.382604, 0.921774, -0.062825, 0.000000, -0.695244, -0.242459, 0.676645, 0.000000, 0.271616, 0.000000, -0.000000, 1.000000" _index_="19"/>
			</bones>
		</skeleton>
		<scripts>
			<script name="TORSimpleObjectAnimator">
				<variables>
					<variable name="AnimationName" value="dwellers_below_tendrils_idle"/>
				</variables>
			</script>
		</scripts>
	</game_entity>
	<game_entity name="FlyableObject" old_prefab_name="" mobility="1">
		<flags>
			<flag name="record_to_scene_replay" value="true"/>
		</flags>
		<tags>
			<tag name="npc_wait"/>
			<tag name="npc_common"/>
		</tags>
		<transform position="0.000, 0.000, 0.000" rotation_euler="0.000, 0.000, 0.000"/>
		<physics shape="bo_bd_chair_a" override_material="wood"/>
		<components>
			<meta_mesh_component name="bd_chair_a"/>
		</components>
		<scripts>
			<script name="Chair">
				<variables>
					<variable name="ChairType" value="Chair"/>
					<variable name="PilotStandingPointTag" value="Pilot"/>
					<variable name="AmmoPickUpTag" value="ammopickup"/>
					<variable name="WaitStandingPointTag" value="Wait"/>
					<variable name="NavMeshPrefabName" value=""/>
				</variables>
			</script>
			<script name="PlayerFlyableObjectScript">
	
				<variables>
				</variables>
			</script>
		</scripts>
		<children>
			<game_entity name="chair_sit_position_front" old_prefab_name="" mobility="1">
				<visibility_masks>
					<visibility_mask name="visible_only_when_editing" value="true"/>
				</visibility_masks>
				<transform position="0.000, -0.600, 0.000" rotation_euler="0.000, 0.000, 3.141" scale="0.750, 0.750, 1.000"/>
				<components>
					<meta_mesh_component name="animation_stand_arrow_blue"/>
				</components>
				<scripts>
					<script name="ChairUsePoint">
						<variables>
							<variable name="NearTable" value="false"/>
							<variable name="NearTableLoopAction" value=""/>
							<variable name="NearTablePairLoopAction" value=""/>
							<variable name="Drink" value="false"/>
							<variable name="DrinkLoopAction" value=""/>
							<variable name="DrinkPairLoopAction" value=""/>
							<variable name="DrinkRightHandItem" value=""/>
							<variable name="DrinkLeftHandItem" value=""/>
							<variable name="Eat" value="false"/>
							<variable name="EatLoopAction" value=""/>
							<variable name="EatPairLoopAction" value=""/>
							<variable name="EatRightHandItem" value=""/>
							<variable name="EatLeftHandItem" value=""/>
							<variable name="ArriveAction" value="act_sit_down_from_front"/>
							<variable name="LoopStartAction" value="act_sit_1"/>
							<variable name="PairLoopStartAction" value=""/>
							<variable name="LeaveAction" value="act_stand_up_to_front"/>
							<variable name="GroupId" value="-1"/>
							<variable name="RightHandItem" value=""/>
							<variable name="RightHandItemBone" value="ItemR"/>
							<variable name="LeftHandItem" value=""/>
							<variable name="LeftHandItemBone" value="ItemL"/>
							<variable name="MinUserToStartInteraction" value="1"/>
							<variable name="ActivatePairs" value="false"/>
							<variable name="MinWaitinSeconds" value="50.000"/>
							<variable name="MaxWaitInSeconds" value="320.000"/>
							<variable name="ForwardDistanceToPivotPoint" value="0.000"/>
							<variable name="SideDistanceToPivotPoint" value="0.000"/>
							<variable name="KeepOldVisibility" value="false"/>
							<variable name="AutoSheathWeapons" value="false"/>
							<variable name="AutoEquipWeaponsOnUseStopped" value="false"/>
							<variable name="AutoWieldWeapons" value="false"/>
							<variable name="TranslateUser" value="true"/>
							<variable name="HasRecentlyBeenRechecked" value="false"/>
							<variable name="NavMeshPrefabName" value=""/>
						</variables>
					</script>
				</scripts>
			</game_entity>
		</children>
	</game_entity>
</prefabs>

