using TaleWorlds.MountAndBlade;
using TaleWorlds.Engine;
using TaleWorlds.Library;
using LOL_Core.HeroSkillSystem.Core;
using TOR_Core.AbilitySystem.Scripts;

namespace LOL_Core.HeroSkillSystem.Skills.Nasus
{
    public class SiphoningStrikeScript : AbilityScript
    {
        private float _effectDuration = 2f;
        private float _timeElapsed = 0f;
        private bool _effectStarted = false;
        private GameEntity _effectEntity;
        private Light _purpleLight;
        private HeroAbility _heroAbility;

        public override void Initialize(TOR_Core.AbilitySystem.Ability ability)
        {
            base.Initialize(ability);
            _effectDuration = ability.Template.Duration;
            
            if (ability is HeroAbility heroAbility)
            {
                _heroAbility = heroAbility;
            }
            
            CreateVisualEffects();
        }

        protected override void OnBeforeTick(float dt)
        {
            base.OnBeforeTick(dt);
            
            if (!_effectStarted)
            {
                StartEffect();
                _effectStarted = true;
            }

            _timeElapsed += dt;
            
            UpdateVisualEffects(dt);
            
            if (_timeElapsed >= _effectDuration)
            {
                Stop();
            }
        }

        private void StartEffect()
        {
            if (CasterAgent != null && CasterAgent.IsActive())
            {
                PlayCastAnimation();
                PlaySoundEffect();
            }
        }

        private void PlayCastAnimation()
        {
            if (!string.IsNullOrEmpty(Ability.Template.AnimationActionName) && CasterAgent != null)
            {
                CasterAgent.SetActionChannel(1, ActionIndexCache.Create(Ability.Template.AnimationActionName));
            }
        }

        private void PlaySoundEffect()
        {
            if (!string.IsNullOrEmpty(Ability.Template.SoundEffectToPlay) && CasterAgent != null)
            {
                Mission.Current.MakeSound(
                    SoundEvent.GetEventIdFromString(Ability.Template.SoundEffectToPlay),
                    CasterAgent.Position,
                    false,
                    true,
                    CasterAgent.Index,
                    -1);
            }
        }

        private void CreateVisualEffects()
        {
            if (CasterAgent == null) return;

            CreatePurpleGlow();
            
            if (!string.IsNullOrEmpty(Ability.Template.ParticleEffectPrefab))
            {
                CreateParticleEffect();
            }
        }

        private void CreatePurpleGlow()
        {
            if (!Ability.Template.HasLight) return;

            _purpleLight = Light.CreatePointLight(3f);
            _purpleLight.Intensity = 2f;
            _purpleLight.LightColor = new Vec3(138, 43, 226); // 紫色
            _purpleLight.SetShadowType(Light.ShadowType.DynamicShadow);
            _purpleLight.ShadowEnabled = true;
            _purpleLight.SetLightFlicker(0.1f, 0.1f);
            _purpleLight.Frame = MatrixFrame.Identity;
            _purpleLight.SetVisibility(true);

            if (GameEntity != null)
            {
                GameEntity.AddLight(_purpleLight);
            }
        }

        private void CreateParticleEffect()
        {
            if (GameEntity != null && !string.IsNullOrEmpty(Ability.Template.ParticleEffectPrefab))
            {
                _effectEntity = GameEntity.CreateEmpty(Mission.Current.Scene, true);
                if (_effectEntity != null)
                {
                    var frame = CasterAgent.Frame;
                    frame.origin.z += 1.5f; // 提高特效位置
                    _effectEntity.SetGlobalFrame(frame);
                    
                    if (Ability.Template.ParticleEffectSizeModifier != 1f)
                    {
                        var scale = Vec3.One * Ability.Template.ParticleEffectSizeModifier;
                        _effectEntity.SetFrame(ref frame);
                    }
                }
            }
        }

        private void UpdateVisualEffects(float dt)
        {
            if (_purpleLight != null && CasterAgent != null)
            {
                var progress = _timeElapsed / _effectDuration;
                var intensity = 2f * (1f - progress); // 逐渐减弱
                _purpleLight.Intensity = MathF.Max(0.1f, intensity);
                
                var lightFrame = CasterAgent.Frame;
                lightFrame.origin.z += CasterAgent.GetEyeGlobalHeight();
                _purpleLight.Frame = lightFrame;
            }

            if (_effectEntity != null && CasterAgent != null)
            {
                var entityFrame = CasterAgent.Frame;
                entityFrame.origin.z += 1.5f;
                _effectEntity.SetGlobalFrame(entityFrame);
            }
        }

        protected override void OnBeforeRemoved(int removeReason)
        {
            CleanupEffects();
            base.OnBeforeRemoved(removeReason);
        }

        private void CleanupEffects()
        {
            if (_purpleLight != null)
            {
                _purpleLight.SetVisibility(false);
                _purpleLight = null;
            }

            if (_effectEntity != null)
            {
                _effectEntity.Remove(0);
                _effectEntity = null;
            }
        }

        public void StopAbility()
        {
            CleanupEffects();
            Stop();
        }

        public void OnAgentKilled(Agent killedAgent)
        {
            if (_heroAbility != null && CasterAgent != null)
            {
                _heroAbility.ProcessKill(killedAgent, CasterAgent);
                CreateKillEffect(killedAgent);
            }
        }

        private void CreateKillEffect(Agent killedAgent)
        {
            if (killedAgent == null) return;

            var killLight = Light.CreatePointLight(2f);
            killLight.Intensity = 3f;
            killLight.LightColor = new Vec3(255, 215, 0); // 金色
            killLight.Frame = new MatrixFrame(Mat3.Identity, killedAgent.Position + new Vec3(0, 0, 1));
            killLight.SetVisibility(true);

            var lightEntity = GameEntity.CreateEmpty(Mission.Current.Scene);
            lightEntity.SetGlobalFrame(killLight.Frame);
            // 简化处理，直接设置光源位置而不添加到场景

            // 简化定时器处理
            // Mission.Current.AddTimerToCurrent(1f, () => {
            //     killLight.SetVisibility(false);
            // });
        }
    }
}
