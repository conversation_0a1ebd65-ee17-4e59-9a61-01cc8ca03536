using System.Collections.Generic;
using System.Linq;
using TaleWorlds.CampaignSystem;
using LOL_Core.Extensions;
using LOL_Core.HeroSkillSystem.Data;

namespace LOL_Core.HeroSkillSystem.Core
{
    public static class HeroSkillManager
    {
        public static void InitializeHeroSkills(Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            if (skillData == null)
            {
                skillData = new HeroSkillData(hero.StringId);
                hero.SetHeroSkillData(skillData);
            }

            if (skillData.UnlockedSkills.Count == 0)
            {
                UnlockDefaultSkill(hero);
            }
        }

        public static void UnlockDefaultSkill(Hero hero)
        {
            const string defaultSkillID = "nasus_siphoning_strike";
            hero.UnlockHeroSkill(defaultSkillID);
            hero.EquipHeroSkill(defaultSkillID);
        }

        public static List<string> GetAvailableSkillsForHero(Hero hero)
        {
            var allSkills = HeroAbilityFactory.GetAllSkillIDs();
            var unlockedSkills = hero.GetHeroSkillData()?.UnlockedSkills ?? new List<string>();
            
            return allSkills.Where(skillID => 
            {
                var template = HeroAbilityFactory.GetTemplate(skillID);
                return template != null && 
                       hero.Level >= template.RequiredLevel &&
                       !unlockedSkills.Contains(skillID);
            }).ToList();
        }

        public static List<string> GetUnlockedSkills(Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData?.UnlockedSkills ?? new List<string>();
        }

        public static bool CanUnlockSkill(Hero hero, string skillID)
        {
            if (hero.IsHeroSkillUnlocked(skillID)) return false;
            
            var template = HeroAbilityFactory.GetTemplate(skillID);
            if (template == null) return false;
            
            return hero.Level >= template.RequiredLevel;
        }

        public static bool TryUnlockSkill(Hero hero, string skillID)
        {
            if (!CanUnlockSkill(hero, skillID)) return false;
            
            hero.UnlockHeroSkill(skillID);
            return true;
        }

        public static bool CanEquipSkill(Hero hero, string skillID)
        {
            return hero.IsHeroSkillUnlocked(skillID);
        }

        public static bool TryEquipSkill(Hero hero, string skillID)
        {
            if (!CanEquipSkill(hero, skillID)) return false;
            
            hero.EquipHeroSkill(skillID);
            return true;
        }

        public static string GetEquippedSkill(Hero hero)
        {
            return hero.GetEquippedSkillID();
        }

        public static void UnequipSkill(Hero hero)
        {
            hero.UnequipHeroSkill();
        }

        public static SkillGrowthData GetSkillGrowthData(Hero hero, string skillID)
        {
            return hero.GetSkillGrowthData(skillID);
        }

        public static int GetSkillStacks(Hero hero, string skillID)
        {
            return hero.GetSkillStacks(skillID);
        }

        public static int GetSkillKills(Hero hero, string skillID)
        {
            return hero.GetSkillKills(skillID);
        }

        public static float GetSkillDamage(Hero hero, string skillID)
        {
            var template = HeroAbilityFactory.GetTemplate(skillID);
            if (template == null || !template.CanGrow) return template?.BaseDamage ?? 0f;
            
            var stacks = GetSkillStacks(hero, skillID);
            return template.BaseDamage + (stacks * template.GrowthPerKill);
        }

        public static string GetSkillDescription(Hero hero, string skillID)
        {
            var template = HeroAbilityFactory.GetTemplate(skillID);
            if (template == null) return "";
            
            var description = template.Description;
            
            if (template.CanGrow)
            {
                var stacks = GetSkillStacks(hero, skillID);
                var kills = GetSkillKills(hero, skillID);
                var currentDamage = GetSkillDamage(hero, skillID);
                
                description += $"\n当前层数: {stacks}";
                description += $"\n当前伤害: {currentDamage:F0}";
                description += $"\n总击杀数: {kills}";
            }
            
            return description;
        }

        public static void ResetSkillGrowth(Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            skillData?.ResetSkillGrowth(skillID);
            if (skillData != null)
            {
                hero.SetHeroSkillData(skillData);
            }
        }

        public static void ResetAllSkillGrowth(Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            skillData?.ResetAllSkills();
            if (skillData != null)
            {
                hero.SetHeroSkillData(skillData);
            }
        }

        public static List<HeroAbilityTemplate> GetUnlockedSkillTemplates(Hero hero)
        {
            var unlockedSkills = GetUnlockedSkills(hero);
            var templates = new List<HeroAbilityTemplate>();
            
            foreach (var skillID in unlockedSkills)
            {
                var template = HeroAbilityFactory.GetTemplate(skillID);
                if (template != null)
                {
                    templates.Add(template);
                }
            }
            
            return templates;
        }

        public static int GetTotalSkillExperience(Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData?.TotalSkillExperience ?? 0;
        }
    }
}
