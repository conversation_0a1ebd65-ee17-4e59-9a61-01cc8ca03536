//Important Irregular Characters
    //| (Vertical Bar)

//Scenarios notes
    //Rarity: COMMON/UNCOMMON/RARE
    //Repeatable: YES/NO
    
    //Restrictions
        //Character Level:
        //Race:
        //Faction:
        //
    
    //Triggers:
        //
    
    //Scenario Explanation (explain the main scenario and any major variations that you are planning to build in. If a variation is different enough consider making it its own file.)
    
        //Main:

        //Alt
        
//Data Import/Export Section
    //Make sure you include this in all ink files to get access to integration functions
        INCLUDE include.ink
        
    //List of Data Being Imported (use this to help keep track of what data you are importing; will help with troubleshooting and testing.)
    
        //
        
    //Data Exported (use this to help keep track of what data you are exporting; will help with troubleshooting and testing.)
        //
        
//Variables setup
    //IMPORTANT! Initial values are mandatory, but they can only be primitives (number, string, boolean). If we want to assign the return value of a function to the variable, we must do it on a separate line, see one line below

    //Seed
        //~ SEED_RANDOM(100) //Uncomment to lock an RNG testing seed for the randomness. Change number inside () for different seed
        
    //
        
//Variable Check (Use for sanity check. Uncomment variables to see what they are)


-> Start

===Start===


-> END 
