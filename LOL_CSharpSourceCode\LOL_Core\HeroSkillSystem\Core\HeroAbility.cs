using System;
using TaleWorlds.Core;
using TaleWorlds.MountAndBlade;
using TaleWorlds.Localization;
using TOR_Core.AbilitySystem;
using TOR_Core.AbilitySystem.Scripts;
using TOR_Core.Extensions;
using LOL_Core.HeroSkillSystem.Data;
using LOL_Core.Extensions;

namespace LOL_Core.HeroSkillSystem.Core
{
    public abstract class HeroAbility : Ability
    {
        public HeroAbilityTemplate HeroTemplate => Template as HeroAbilityTemplate;
        public SkillGrowthData GrowthData { get; protected set; }
        
        public delegate void OnKillConfirmedHandler(Agent killedAgent, Agent killerAgent);
        public event OnKillConfirmedHandler OnKillConfirmed;

        public HeroAbility(HeroAbilityTemplate template) : base(template)
        {
            GrowthData = new SkillGrowthData
            {
                SkillID = template.StringID,
                CurrentStacks = 0,
                TotalKills = 0
            };
        }

        public virtual void LoadGrowthData(SkillGrowthData data)
        {
            if (data != null && data.SkillID == StringID)
            {
                GrowthData = data;
            }
        }

        public virtual SkillGrowthData SaveGrowthData()
        {
            return GrowthData;
        }

        public virtual float GetCurrentDamage()
        {
            if (HeroTemplate.CanGrow)
            {
                return HeroTemplate.BaseDamage + (GrowthData.CurrentStacks * HeroTemplate.GrowthPerKill);
            }
            return HeroTemplate.BaseDamage;
        }

        public virtual void ProcessKill(Agent killedAgent, Agent killerAgent)
        {
            if (HeroTemplate.CanGrow && killerAgent.IsMainAgent)
            {
                GrowthData.CurrentStacks++;
                GrowthData.TotalKills++;
                OnKillConfirmed?.Invoke(killedAgent, killerAgent);
                SaveGrowthDataToHero(killerAgent);
            }
        }

        protected virtual void SaveGrowthDataToHero(Agent agent)
        {
            var hero = agent.GetHero();
            if (hero != null)
            {
                var skillData = hero.GetHeroSkillData();
                if (skillData != null)
                {
                    skillData.UpdateSkillGrowth(GrowthData);
                }
            }
        }

        public override bool CanCast(Agent casterAgent, out TextObject failureReason)
        {
            if (!base.CanCast(casterAgent, out failureReason))
            {
                return false;
            }

            if (HeroTemplate.RequiredLevel > 0)
            {
                var hero = casterAgent.GetHero();
                if (hero != null && hero.Level < HeroTemplate.RequiredLevel)
                {
                    failureReason = new TextObject("Level {LEVEL} required").SetTextVariable("LEVEL", HeroTemplate.RequiredLevel);
                    return false;
                }
            }

            return true;
        }

        public override void ActivateAbility(Agent casterAgent)
        {
            base.ActivateAbility(casterAgent);
        }

        public virtual string GetSkillDescription()
        {
            var description = HeroTemplate.Description;
            if (HeroTemplate.CanGrow)
            {
                description += $"\n当前层数: {GrowthData.CurrentStacks}";
                description += $"\n当前伤害: {GetCurrentDamage():F0}";
                description += $"\n总击杀数: {GrowthData.TotalKills}";
            }
            return description;
        }
    }
}
