using System;
using System.Xml.Serialization;

namespace LOL_Core.HeroSkillSystem.Data
{
    [Serializable]
    public class SkillGrowthData
    {
        [XmlAttribute]
        public string SkillID { get; set; } = "";
        
        [XmlAttribute]
        public int CurrentStacks { get; set; } = 0;
        
        [XmlAttribute]
        public int TotalKills { get; set; } = 0;
        
        [XmlAttribute]
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;
        
        [XmlAttribute]
        public float BonusDamage { get; set; } = 0f;
        
        [XmlAttribute]
        public int ExperienceGained { get; set; } = 0;

        public SkillGrowthData()
        {
        }

        public SkillGrowthData(string skillID)
        {
            SkillID = skillID;
            CurrentStacks = 0;
            TotalKills = 0;
            LastUpdated = DateTime.UtcNow;
            BonusDamage = 0f;
            ExperienceGained = 0;
        }

        public void AddKill()
        {
            CurrentStacks++;
            TotalKills++;
            LastUpdated = DateTime.UtcNow;
            ExperienceGained += 10;
        }

        public void AddStacks(int stacks)
        {
            CurrentStacks += stacks;
            LastUpdated = DateTime.UtcNow;
        }

        public void SetBonusDamage(float damage)
        {
            BonusDamage = damage;
            LastUpdated = DateTime.UtcNow;
        }

        public void Reset()
        {
            CurrentStacks = 0;
            TotalKills = 0;
            BonusDamage = 0f;
            ExperienceGained = 0;
            LastUpdated = DateTime.UtcNow;
        }

        public SkillGrowthData Clone()
        {
            return new SkillGrowthData
            {
                SkillID = this.SkillID,
                CurrentStacks = this.CurrentStacks,
                TotalKills = this.TotalKills,
                LastUpdated = this.LastUpdated,
                BonusDamage = this.BonusDamage,
                ExperienceGained = this.ExperienceGained
            };
        }
    }
}
