# LOL英雄技能系统需求文档

## 1. 项目概述

### 1.1 项目背景
基于Mount & Blade II: Bannerlord游戏引擎，开发一个英雄联盟(LOL)主题的模组，为玩家提供LOL的MMO游戏体验。

### 1.2 项目目标
- 实现英雄技能装备系统
- 提供技能战斗使用机制
- 构建技能成长系统
- 创建直观的UI交互界面

## 2. 功能需求

### 2.1 核心功能需求

#### 2.1.1 技能装备系统
**需求描述**: 玩家可以选择并装备英雄技能
- **装备界面**: 在装备栏显示技能槽位
- **技能选择**: 提供技能库供玩家选择装备
- **装备限制**: 每个玩家最多装备1个技能
- **技能切换**: 支持在非战斗状态下更换技能

#### 2.1.2 战斗技能使用
**需求描述**: 玩家在战场中可以使用装备的技能
- **快捷键使用**: 通过R键使用对应技能
- **冷却机制**: 技能使用后有冷却时间
- **目标选择**: 支持多种目标选择模式（单体/AOE/自身）
- **视觉反馈**: 技能使用有相应的视觉和音效

#### 2.1.3 技能成长系统
**需求描述**: 技能可以通过特定条件永久增强
- **成长触发**: 基于特定游戏事件触发成长
- **数据持久**: 成长数据在游戏会话间保持
- **成长显示**: UI显示技能当前成长状态

### 2.2 特定技能需求 - 内瑟斯死亡汲取

#### 2.2.1 技能基础属性
- **技能名称**: 死亡汲取 (Siphoning Strike)
- **技能类型**: 主动技能
- **冷却时间**: 8秒
- **施法范围**: 近战范围
- **目标类型**: 单体敌方单位

#### 2.2.2 技能效果
- **基础伤害**: 50点穿刺伤害
- **成长机制**: 每次使用此技能击败一个单位，技能伤害永久增加1点
- **视觉效果**: 挥击时显示紫色能量效果
- **音效**: 技能使用时播放汲取音效（音频文件）

#### 2.2.3 成长系统细节
- **成长条件**: 必须是技能最后一击击败单位
- **成长记录**: 记录总击败数和当前额外伤害
- **成长显示**: 技能图标显示当前叠加层数
- **数据保存**: 成长数据保存在角色存档中

## 3. 非功能需求

### 3.1 兼容性需求
- **游戏版本**: 兼容Bannerlord 1.2.12版本
- **依赖项**: 基于TOR_Core框架构建

### 3.3 可用性需求
- **界面语言**: 支持中文/英文界面
- **错误处理**: 提供清晰的错误信息和恢复机制

## 4. 用户场景

### 4.1 技能装备场景
1. 玩家进入装备界面
2. 查看可用技能列表
3. 选择要装备的技能
4. 将技能拖拽到技能槽位
5. 确认装备并保存配置

### 4.2 战斗使用场景
1. 玩家进入战斗状态
2. 按下技能快捷键R
3. 选择技能目标(如需要)
4. 技能施放并产生效果
5. 进入冷却状态

### 4.3 技能成长场景
1. 玩家使用死亡汲取技能
2. 技能击败敌方单位
3. 系统检测击败条件
4. 技能伤害永久增加1点
5. UI更新技能成长信息

## 5. 约束条件

### 5.1 技术约束
- 必须在Bannerlord模组框架内实现
- 使用C#作为主要开发语言
- 遵循TOR_Core的架构模式
- 使用XML进行配置管理

### 5.2 设计约束
- 保持与Bannerlord原生UI风格一致，可使用lol的风格
- 技能效果不能破坏游戏平衡性
- 兼容现有的存档系统

## 6. 验收标准

### 6.1 功能验收
- ✅ 玩家可以成功装备/卸载技能
- ✅ 技能在战斗中正常使用和冷却
- ✅ 死亡汲取技能成长机制正常工作
- ✅ 技能数据正确保存和加载

### 6.2 性能验收
- ✅ 技能使用无明显卡顿

### 6.3 用户体验验收
- ✅ UI操作直观易懂
- ✅ 视觉效果美观流畅
- ✅ 错误情况有合理提示

## 8. 后续扩展规划

### 8.1 短期扩展
- 增加更多英雄技能
- 完善视觉特效系统
- 添加技能音效库

### 8.2 长期扩展
- 构建完整英雄系统
- 实现装备和技能联动
- 开发AI使用技能机制