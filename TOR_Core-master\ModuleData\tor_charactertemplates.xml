﻿<?xml version="1.0" encoding="UTF-8"?>
<NPCCharacters>
  <NPCCharacter id="tor_wanderer_empire_0" default_group="ranged" name="{=str_tor_wanderer_empire_0}{FIRSTNAME} the Bright Wizard" occupation="Wanderer" culture="Culture.empire" voice="curt" age="55" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.brightwizard" />
      <hair_tags>
        <hair_tag name="Bandit Hair 3" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_bright_wizard_template" />
      <EquipmentSet id="tor_bright_wizard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_1" default_group="infantry" name="{=str_tor_wanderer_empire_1}{FIRSTNAME} the Warrior Priest" occupation="Wanderer" culture="Culture.empire" voice="curt" age="45" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.warriorpriest" />
      <hair_tags>
        <hair_tag name="Bald" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="5" />
      <Trait id="Commander" value="3" />
      <Trait id="Surgeon" value="5" />
      <Trait id="Calculating" value="1" />
      <Trait id="Mercy" value="3" />
      <Trait id="Valor" value="1" />
      <Trait id="SigmarDevoted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_warrior_priest_template" />
      <EquipmentSet id="tor_warrior_priest_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_2" default_group="infantry" name="{=str_tor_wanderer_empire_2}{FIRSTNAME} the Witch Hunter" occupation="Wanderer" culture="Culture.empire" voice="curt" age="29" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="LongAndBushy" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Mustache_and_patch" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="5" />
      <Trait id="RogueSkills" value="3" />
      <Trait id="ScoutSkills" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="Gunner" value="4" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_witch_hunter_template" />
      <EquipmentSet id="tor_witch_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_3" default_group="ranged" name="{=str_tor_wanderer_empire_3}{FIRSTNAME} the Light Wizard" occupation="Wanderer" culture="Culture.empire" voice="curt" age="50" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_light_wizard_template" />
      <EquipmentSet id="tor_light_wizard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_4" default_group="ranged" name="{=str_tor_wanderer_empire_4}{FIRSTNAME} the Celestial Wizard" occupation="Wanderer" culture="Culture.empire" voice="curt" age="50" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_celestial_wizard_template" />
      <EquipmentSet id="tor_celestial_wizard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_5" default_group="infantry" name="{=str_tor_wanderer_empire_5}{FIRSTNAME} the Engineer" occupation="Wanderer" culture="Culture.empire" voice="curt" age="49" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="6" />
      <Trait id="EngineerSkills" value="7" />
      <Trait id="Blacksmith" value="4" />
      <Trait id="Calculating" value="-1" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="Gunner" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_engineer_companion_template" />
      <EquipmentSet id="tor_engineer_companion_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_6" default_group="infantry" name="{=str_tor_wanderer_empire_6}{FIRSTNAME} the Priestess of Shallya" occupation="Wanderer" culture="Culture.empire" voice="curt" age="31" is_female="true" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.townswoman_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Surgeon" value="8" />
      <Trait id="Calculating" value="1" />
      <Trait id="Mercy" value="5" />
      <Trait id="Valor" value="7" />
      <Trait id="ShallyaDevoted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_priestess_of_shallya_template" />
      <EquipmentSet id="tor_priestess_of_shallya_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_7" default_group="infantry" name="{=str_tor_wanderer_empire_7}{FIRSTNAME} the Huntsman" occupation="Wanderer" culture="Culture.empire" voice="curt" age="42" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="ArcherFIghtingSkills" value="8" />
      <Trait id="ScoutSkills" value="6" />
      <Trait id="RogueSkills" value="3" />
      <Trait id="Valor" value="1" />
      <Trait id="Honor" value="1" />
      <Trait id="Generosity" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_huntsman_wanderer_template" />
      <EquipmentSet id="tor_huntsman_wanderer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_8" default_group="infantry" name="{=str_tor_wanderer_empire_8}{FIRSTNAME} the Gold Wizard" occupation="Wanderer" culture="Culture.empire" voice="curt" age="44" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="Bald" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
      <Trait id="Blacksmith" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_gold_wizard_template" />
      <EquipmentSet id="tor_gold_wizard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_9" default_group="infantry" name="{=str_tor_wanderer_empire_9}{FIRSTNAME} the Life Wizard" occupation="Wanderer" culture="Culture.empire" voice="softspoken" age="25" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_life_wizard_template" />
      <EquipmentSet id="tor_life_wizard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_empire_10" default_group="infantry" name="{=str_tor_wanderer_empire_10}{FIRSTNAME} the Beast Wizard" occupation="Wanderer" culture="Culture.empire" voice="curt" age="50" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_beast_wizard_template" />
      <EquipmentSet id="tor_beast_wizard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_bretonnia_0" default_group="infantry" name="{=str_tor_wanderer_bretonnia_0}{FIRSTNAME} the Bretonnian Knight" occupation="Wanderer" culture="Culture.vlandia" voice="curt" age="38" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_bretonnian_knight_template" />
      <EquipmentSet id="tor_bretonnian_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_bretonnia_1" default_group="infantry" name="{=str_tor_wanderer_bretonnia_1}{FIRSTNAME} the Damsel of the Lady" occupation="Wanderer" culture="Culture.vlandia" voice="curt" age="30" is_female="true" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.townswoman_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_prophetess_template" />
      <EquipmentSet id="tor_prophetess_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_mousillon_0" default_group="infantry" name="{=str_tor_wanderer_mousillon_0}{FIRSTNAME} the Fallen Knight" occupation="Wanderer" culture="Culture.mousillon" voice="curt" age="38" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="1" />
      <Trait id="NagashCorrupted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_fallen_knight" />
      <EquipmentSet id="tor_fallen_knight" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_mousillon_1" default_group="infantry" name="{=str_tor_wanderer_mousillon_1}{FIRSTNAME} the Necromancer of Mousillon" occupation="Wanderer" culture="Culture.mousillon" voice="ironic" age="55" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="7" />
      <Trait id="NagashCorrupted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_necromancer_template" />
      <EquipmentSet id="tor_necromancer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_vampire_0" default_group="infantry" name="{=str_tor_wanderer_vampire_0}{FIRSTNAME} the Necromancer" occupation="Wanderer" culture="Culture.khuzait" voice="ironic" age="55" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="7" />
      <Trait id="NagashCorrupted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_necromancer_template" />
      <EquipmentSet id="tor_necromancer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_vampire_1" default_group="cavalry" name="{=str_tor_wanderer_vampire_1}{FIRSTNAME} the Vampire" occupation="Wanderer" culture="Culture.khuzait" voice="ironic" age="19" race="vampire" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.vampire" />
      <hair_tags>
        <hair_tag name="ShortAndThin" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="6" />
      <Trait id="Manager" value="3" />
      <Trait id="Commander" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="5" />
      <Trait id="NagashCorrupted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_generic_vampire_template" />
      <EquipmentSet id="tor_generic_vampire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_vampire_2" default_group="infantry" name="{=str_tor_wanderer_vampire_2}{FIRSTNAME} the Wight King" occupation="Wanderer" culture="Culture.khuzait" voice="curt" age="30" race="skeleton" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="NagashCorrupted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_wight_king_template" />
      <EquipmentSet id="tor_wight_king_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_vampire_3" default_group="cavalry" name="{=str_tor_wanderer_vampire_3}{FIRSTNAME} the Lahmian Countess" occupation="Wanderer" culture="Culture.khuzait" voice="ironic" age="19" race="vampire" is_female="true" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.vampire_female" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="6" />
      <Trait id="Manager" value="3" />
      <Trait id="Commander" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="5" />
      <Trait id="NagashCorrupted" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_vc_lahmian_countess_template" />
      <EquipmentSet id="tor_vc_lahmian_countess_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_woodelf_0" default_group="infantry" name="{=str_tor_wanderer_woodelf_0}{FIRSTNAME} the Spellsinger" occupation="Wanderer" culture="Culture.battania" voice="softspoken" age="19" race="elf" is_female="true" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.female_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_we_spellsinger_template" />
      <EquipmentSet id="tor_we_spellsinger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_woodelf_1" default_group="infantry" name="{=str_tor_wanderer_woodelf_1}{FIRSTNAME} the Glade Captain" occupation="Wanderer" culture="Culture.battania" voice="curt" age="19" race="elf" is_female="true" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.female_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="ScoutSkills" value="4" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_we_glade_captain_template" />
      <EquipmentSet id="tor_we_glade_captain_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_woodelf_2" default_group="infantry" name="{=str_tor_wanderer_woodelf_2}{FIRSTNAME} the Waywatcher" occupation="Wanderer" culture="Culture.battania" voice="curt" age="22" race="elf" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="3" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="ScoutSkills" value="6" />
      <Trait id="ArcherFIghtingSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_we_waywatcher_template" />
      <EquipmentSet id="tor_we_waywatcher_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_eonir_0" default_group="infantry" name="{=str_tor_wanderer_eonir_0}{FIRSTNAME} the Ghost Strider" occupation="Wanderer" culture="Culture.eonir" voice="curt" age="22" race="elf" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="3" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Valor" value="-1" />
      <Trait id="RogueSkills" value="-1" />
      <Trait id="ScoutSkills" value="7" />
      <Trait id="ArcherFIghtingSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_eo_ghoststrider_template" />
      <EquipmentSet id="tor_eo_ghoststrider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_eonir_1" default_group="infantry" name="{=str_tor_wanderer_eonir_1}{FIRSTNAME} the Guardian Mage" occupation="Wanderer" culture="Culture.eonir" voice="ironic" age="30" race="elf" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="1" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="1" />
      <Trait id="Valor" value="1" />
      <Trait id="SpellCasterSkills" value="7" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_eo_guardian_mage_template" />
      <EquipmentSet id="tor_eo_guardian_mage_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter_lord_0" default_group="cavalry" name="{=str_tor_empire_deserter_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_leader_template" />
      <EquipmentSet id="tor_empire_deserter_leader_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter_lord_1" default_group="cavalry" name="{=str_tor_empire_deserter_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_leader_template" />
      <EquipmentSet id="tor_empire_deserter_leader_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter_lord_2" default_group="cavalry" name="{=str_tor_empire_deserter_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_leader_2h_template" />
      <EquipmentSet id="tor_empire_deserter_leader_2h_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter_lord_3" default_group="infantry" name="{=str_tor_empire_deserter_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_leader_2h_template" />
      <EquipmentSet id="tor_empire_deserter_leader_2h_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_rogue_lord_0" default_group="cavalry" name="{=str_tor_br_rogue_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.vlandia" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_m_knight_of_misfortune_template" />
      <EquipmentSet id="tor_m_knight_of_misfortune_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_rogue_lord_0" default_group="cavalry" name="{=str_tor_m_rogue_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.mousillon" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_m_knight_of_misfortune_template" />
      <EquipmentSet id="tor_m_knight_of_misfortune_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_bergerac_lord" default_group="ranged" name="{=str_tor_br_bergerac_lord}{DOESNTMATTER]" occupation="Lord" culture="Culture.vlandia" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_ror_bergerac_bowman_template" />
      <EquipmentSet id="tor_ror_bergerac_bowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_peasantknight_lord" default_group="cavalry" name="{=str_tor_br_peasantknight_lord}{DOESNTMATTER]" occupation="Lord" culture="Culture.vlandia" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_peasant_knight_leader_template" />
      <EquipmentSet id="tor_peasant_knight_leader_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_troubadour_questing_knight_1" default_group="cavalry" name="{=str_tor_br_troubadour_questing_knight_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.vlandia" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_bretonnian_generic_lord_template_quest_1" />
      <EquipmentSet id="tor_bretonnian_generic_lord_template_quest_1" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_troubadour_questing_knight_2" default_group="cavalry" name="{=str_tor_br_troubadour_questing_knight_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.vlandia" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_bretonnian_generic_lord_template_quest_2" />
      <EquipmentSet id="tor_bretonnian_generic_lord_template_quest_2" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog1_lord_0" default_group="infantry" name="{=str_tor_empire_dog1_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_braganza_hero_template" />
      <EquipmentSet id="dog_braganza_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog1_lord_1" default_group="infantry" name="{=str_tor_empire_dog1_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_braganza_hero_template" />
      <EquipmentSet id="dog_braganza_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog1_lord_2" default_group="infantry" name="{=str_tor_empire_dog1_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_braganza_hero_template" />
      <EquipmentSet id="dog_braganza_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog1_lord_3" default_group="infantry" name="{=str_tor_empire_dog1_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_braganza_hero_template" />
      <EquipmentSet id="dog_braganza_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog2_lord_0" default_group="infantry" name="{=str_tor_empire_dog2_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" race="skeleton" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_cursed_hero_template" />
      <EquipmentSet id="dog_cursed_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog2_lord_1" default_group="infantry" name="{=str_tor_empire_dog2_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" race="skeleton" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_cursed_hero_template" />
      <EquipmentSet id="dog_cursed_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog2_lord_2" default_group="infantry" name="{=str_tor_empire_dog2_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" race="skeleton" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_cursed_hero_template" />
      <EquipmentSet id="dog_cursed_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog2_lord_3" default_group="infantry" name="{=str_tor_empire_dog2_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" race="skeleton" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_cursed_hero_template" />
      <EquipmentSet id="dog_cursed_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog3_lord_0" default_group="infantry" name="{=str_tor_empire_dog3_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_leopard_hero_template" />
      <EquipmentSet id="dog_leopard_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog3_lord_1" default_group="infantry" name="{=str_tor_empire_dog3_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_leopard_hero_template" />
      <EquipmentSet id="dog_leopard_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog3_lord_2" default_group="infantry" name="{=str_tor_empire_dog3_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_leopard_hero_template" />
      <EquipmentSet id="dog_leopard_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog3_lord_3" default_group="infantry" name="{=str_tor_empire_dog3_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_leopard_hero_template" />
      <EquipmentSet id="dog_leopard_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog4_lord_0" default_group="cavalry" name="{=str_tor_empire_dog4_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_voland_hero_template" />
      <EquipmentSet id="dog_voland_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog4_lord_1" default_group="cavalry" name="{=str_tor_empire_dog4_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_voland_hero_template" />
      <EquipmentSet id="dog_voland_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog4_lord_2" default_group="cavalry" name="{=str_tor_empire_dog4_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_voland_hero_template" />
      <EquipmentSet id="dog_voland_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog4_lord_3" default_group="cavalry" name="{=str_tor_empire_dog4_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_voland_hero_template" />
      <EquipmentSet id="dog_voland_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog5_lord_0" default_group="infantry" name="{=str_tor_empire_dog5_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_miragliano_hero_template" />
      <EquipmentSet id="dog_miragliano_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog5_lord_1" default_group="infantry" name="{=str_tor_empire_dog5_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_miragliano_hero_template" />
      <EquipmentSet id="dog_miragliano_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog5_lord_2" default_group="infantry" name="{=str_tor_empire_dog5_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_miragliano_hero_template" />
      <EquipmentSet id="dog_miragliano_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog5_lord_3" default_group="infantry" name="{=str_tor_empire_dog5_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_miragliano_hero_template" />
      <EquipmentSet id="dog_miragliano_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog6_lord_0" default_group="infantry" name="{=str_tor_empire_dog6_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_ricco_hero_template" />
      <EquipmentSet id="dog_ricco_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog6_lord_1" default_group="infantry" name="{=str_tor_empire_dog6_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_ricco_hero_template" />
      <EquipmentSet id="dog_ricco_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog6_lord_2" default_group="infantry" name="{=str_tor_empire_dog6_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_ricco_hero_template" />
      <EquipmentSet id="dog_ricco_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog6_lord_3" default_group="infantry" name="{=str_tor_empire_dog6_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_ricco_hero_template" />
      <EquipmentSet id="dog_ricco_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog7_lord_0" default_group="cavalry" name="{=str_tor_empire_dog7_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_muktar_hero_template" />
      <EquipmentSet id="dog_muktar_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog7_lord_1" default_group="cavalry" name="{=str_tor_empire_dog7_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_muktar_hero_template" />
      <EquipmentSet id="dog_muktar_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog7_lord_2" default_group="cavalry" name="{=str_tor_empire_dog7_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_muktar_hero_template" />
      <EquipmentSet id="dog_muktar_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_dog7_lord_3" default_group="cavalry" name="{=str_tor_empire_dog7_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.empire" voice="curt" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="dog_muktar_hero_template" />
      <EquipmentSet id="dog_muktar_hero_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bw_cultist_lord_0" default_group="cavalry" name="{=str_tor_bw_cultist_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_bw_grand_magister_template" />
      <EquipmentSet id="tor_cult_bw_grand_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bw_cultist_lord_1" default_group="cavalry" name="{=str_tor_bw_cultist_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_bw_grand_magister_template" />
      <EquipmentSet id="tor_cult_bw_grand_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bw_cultist_lord_2" default_group="cavalry" name="{=str_tor_bw_cultist_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_bw_grand_magister_template" />
      <EquipmentSet id="tor_cult_bw_grand_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bw_cultist_lord_3" default_group="cavalry" name="{=str_tor_bw_cultist_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_bw_grand_magister_template" />
      <EquipmentSet id="tor_cult_bw_grand_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_coi_cultist_lord_0" default_group="cavalry" name="{=str_tor_coi_cultist_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_coi_grand_master_template" />
      <EquipmentSet id="tor_cult_coi_grand_master_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_coi_cultist_lord_1" default_group="cavalry" name="{=str_tor_coi_cultist_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_coi_grand_master_template" />
      <EquipmentSet id="tor_cult_coi_grand_master_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_coi_cultist_lord_2" default_group="cavalry" name="{=str_tor_coi_cultist_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_coi_grand_master_template" />
      <EquipmentSet id="tor_cult_coi_grand_master_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_coi_cultist_lord_3" default_group="cavalry" name="{=str_tor_coi_cultist_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_coi_grand_master_template" />
      <EquipmentSet id="tor_cult_coi_grand_master_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_fsf_cultist_lord_0" default_group="cavalry" name="{=str_tor_fsf_cultist_lord_0}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" />
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_fsf_cultist_lord_1" default_group="cavalry" name="{=str_tor_fsf_cultist_lord_1}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" />
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_fsf_cultist_lord_2" default_group="cavalry" name="{=str_tor_fsf_cultist_lord_2}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" />
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_fsf_cultist_lord_3" default_group="cavalry" name="{=str_tor_fsf_cultist_lord_3}{DOESNTMATTER]" occupation="Lord" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="2" />
      <Trait id="Manager" value="3" />
      <Trait id="Surgeon" value="3" />
      <Trait id="Calculating" value="3" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
      <Trait id="SpellCasterSkills" value="3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" />
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wanderer_chaos_0" default_group="infantry" name="{=str_tor_wanderer_chaos_0}{FIRSTNAME} the Chosen of Chaos" occupation="Wanderer" culture="Culture.chaos_culture" voice="curt" age="30" race="chaos_ud_cultist" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" />
      <EquipmentSet id="tor_cult_fsf_archbacillus_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_engineerquesthero" default_group="infantry" name="{=str_tor_engineerquesthero}{=!}Goswin" occupation="Lord" culture="Culture.empire" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_engineerquest_goswin_template" />
      <EquipmentSet id="tor_qnpc_engineerquest_goswin_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bountymaster_empire_0" default_group="infantry" name="{=str_tor_bountymaster_empire_0}{=!}Templar Witch Hunter" occupation="Special" culture="Culture.empire" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-1" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_witchhunter_template" />
      <EquipmentSet id="tor_qnpc_witchhunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_nulnengineernpc_empire" default_group="infantry" name="{=str_tor_nulnengineernpc_empire}{=!}Master Engineer" occupation="Special" culture="Culture.empire" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-2" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_masterengineer_template" />
      <EquipmentSet id="tor_qnpc_masterengineer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_prestige_noble_empire" default_group="infantry" name="{=str_tor_prestige_noble_empire}{=!}Connected Noble" occupation="Special" culture="Culture.empire" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-2" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_prestige_noble_template" />
      <EquipmentSet id="tor_qnpc_prestige_noble_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_spelltrainer_empire_0" default_group="infantry" name="{=str_tor_spelltrainer_empire_0}{=!}College Magister" occupation="Special" culture="Culture.empire" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_collegemagister_template" />
      <EquipmentSet id="tor_qnpc_collegemagister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_spelltrainer_vc_0" default_group="infantry" name="{=str_tor_spelltrainer_vc_0}{=!}Dark Magister" occupation="Special" culture="Culture.khuzait" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-4" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_necromagister_template" />
      <EquipmentSet id="tor_qnpc_necromagister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_spelltrainer_bretonnia_0" default_group="infantry" name="{=str_tor_spelltrainer_bretonnia_0}{=!}The Fay Enchantress" occupation="Special" culture="Culture.vlandia" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fey_enchantress" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_fay_enchantress_template" />
      <EquipmentSet id="tor_fay_enchantress_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_spelltrainer_woodelves_0" default_group="infantry" name="{=str_tor_spelltrainer_woodelves_0}{=!}Wise Spellsinger" occupation="Special" culture="Culture.battania" voice="ironic" age="30" race="elf" is_female="true" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fey_enchantress" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_we_spellsinger_template" />
      <EquipmentSet id="tor_we_spellsinger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eonir_druchii_envoy_0" default_group="infantry" name="{=str_tor_eonir_druchii_envoy_0}{=!}Witch King Envoy" occupation="Special" culture="Culture.eonir" voice="ironic" age="30" race="elf" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-4" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_eonir_envoy_druchii" />
      <EquipmentSet id="tor_qnpc_eonir_envoy_druchii" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eonir_asur_envoy_0" default_group="infantry" name="{=str_tor_eonir_asur_envoy_0}{=!}Phoenix King Envoy" occupation="Special" culture="Culture.eonir" voice="ironic" age="30" race="elf" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-4" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_eonir_envoy_asur" />
      <EquipmentSet id="tor_qnpc_eonir_envoy_asur" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eonir_empire_envoy_0" default_group="infantry" name="{=str_tor_eonir_empire_envoy_0}{=!}Empire Envoy" occupation="Special" culture="Culture.eonir" voice="ironic" age="30" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_eonir_envoy_empire" />
      <EquipmentSet id="tor_qnpc_eonir_envoy_empire" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eonir_spellsinger_envoy_0" default_group="infantry" name="{=str_tor_eonir_spellsinger_envoy_0}{=!}Forestguardian" occupation="Special" culture="Culture.eonir" voice="ironic" age="30" race="elf" is_template="true" is_hero="false">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
    </face>
    <Traits>
      <Trait id="BalancedFightingSkills" value="8" />
      <Trait id="Commander" value="5" />
      <Trait id="Calculating" value="5" />
      <Trait id="Mercy" value="-1" />
      <Trait id="Valor" value="-3" />
    </Traits>
    <Equipments>
      <EquipmentSet id="tor_qnpc_eonir_envoy_spellsinger" />
      <EquipmentSet id="tor_qnpc_eonir_envoy_spellsinger" civilian="true" />
    </Equipments>
  </NPCCharacter>
</NPCCharacters>