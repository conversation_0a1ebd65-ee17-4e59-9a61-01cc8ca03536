using TaleWorlds.CampaignSystem;
using LOL_Core.HeroSkillSystem.Data;
using System.Collections.Generic;

namespace LOL_Core.Extensions
{
    public static class HeroExtensions
    {
        private const string HERO_SKILL_DATA_KEY = "LOL_HeroSkillData";

        // 简单的内存存储，用于替代 TOR_Core 扩展
        private static Dictionary<string, HeroSkillData> _heroSkillDataCache = new Dictionary<string, HeroSkillData>();

        public static HeroSkillData GetHeroSkillData(this Hero hero)
        {
            if (_heroSkillDataCache.TryGetValue(hero.StringId, out var skillData))
            {
                return skillData;
            }

            var newSkillData = new HeroSkillData(hero.StringId);
            hero.SetHeroSkillData(newSkillData);
            return newSkillData;
        }

        public static void SetHeroSkillData(this Hero hero, HeroSkillData skillData)
        {
            _heroSkillDataCache[hero.StringId] = skillData;
        }

        public static bool HasEquippedSkill(this Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData != null && skillData.HasEquippedSkill();
        }

        public static string GetEquippedSkillID(this Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData?.EquippedSkillID ?? "";
        }

        public static void EquipHeroSkill(this Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            if (skillData != null)
            {
                skillData.EquipSkill(skillID);
                hero.SetHeroSkillData(skillData);
            }
        }

        public static void UnequipHeroSkill(this Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            if (skillData != null)
            {
                skillData.UnequipSkill();
                hero.SetHeroSkillData(skillData);
            }
        }

        public static void UnlockHeroSkill(this Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            if (skillData != null)
            {
                skillData.UnlockSkill(skillID);
                hero.SetHeroSkillData(skillData);
            }
        }

        public static bool IsHeroSkillUnlocked(this Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData != null && skillData.IsSkillUnlocked(skillID);
        }

        public static SkillGrowthData GetSkillGrowthData(this Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData?.GetSkillGrowthData(skillID);
        }

        public static int GetSkillStacks(this Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData?.GetCurrentStacksForSkill(skillID) ?? 0;
        }

        public static int GetSkillKills(this Hero hero, string skillID)
        {
            var skillData = hero.GetHeroSkillData();
            return skillData?.GetTotalKillsForSkill(skillID) ?? 0;
        }
    }
}
