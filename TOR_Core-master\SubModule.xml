<Module>
	<Name value="The Old Realms Core Module" />
	<Id value="TOR_Core" />
	<Version value="v1.2.11" />
	<ModuleCategory value="Singleplayer"/>
	<DependedModules>
		<DependedModule Id="Native" DependentVersion="v1.2.11" />
		<DependedModule Id="SandBoxCore" DependentVersion="v1.2.11" />
		<DependedModule Id="Sandbox" DependentVersion="v1.2.11" />
		<DependedModule Id="StoryMode" DependentVersion="v1.2.11" />
		<DependedModule Id="TOR_Armory" DependentVersion="v1.2.11" />
		<DependedModule Id="TOR_Environment" DependentVersion="v1.2.11" />
	</DependedModules>
	<SubModules>
		<SubModule>
			<Name value="TOR_Core" />
			<DLLName value="TOR_Core.dll" />
			<SubModuleClassType value="TOR_Core.SubModule" />
			<Tags>
				<Tag key="DedicatedServerType" value="none" />
				<Tag key="IsNoRenderModeElement" value="false" />
			</Tags>
		</SubModule>
	</SubModules>
	<Xmls>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_pre_npccharacters" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_townspeople" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_troopdefinitions" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_lords" />
			<IncludedGameTypes>
				<GameType value="CustomGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_dummyNPCs" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_campaign_lords" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="NPCCharacters" path="tor_charactertemplates" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="SPCultures" path="tor_pre_cultures" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="SPCultures" path="tor_cultures" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Heroes" path="tor_pre_heroes"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Heroes" path="tor_heroes"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Kingdoms" path="tor_pre_kingdoms"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Kingdoms" path="tor_kingdoms"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Factions" path="tor_pre_clans"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Factions" path="tor_clans"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="SkillSets" path="tor_skillsets" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="BodyProperties" path="tor_bodyproperties" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
				<GameType value="EditorGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="EquipmentRosters" path="tor_equipment_sets" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
				<GameType value="CustomGame" />
				<GameType value="EditorGame" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="partyTemplates" path="tor_pre_partytemplates" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="partyTemplates" path="tor_partytemplates" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Settlements" path="tor_pre_settlements" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Settlements" path="tor_settlements" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="GameText" path="tor_strings" />
		</XmlNode>
		<XmlNode>
			<XmlName id="GameText" path="tor_voiced_strings" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>
			<XmlName id="Religions" path="tor_religions" />
			<IncludedGameTypes>
				<GameType value="Campaign" />
				<GameType value="CampaignStoryMode" />
			</IncludedGameTypes>
		</XmlNode>
		<XmlNode>                
			<XmlName id="Concepts" path="tor_concept_strings"/>
			<IncludedGameTypes>
				<GameType value = "Campaign"/>
				<GameType value = "CampaignStoryMode"/>
			</IncludedGameTypes>
		</XmlNode>  
	</Xmls>
</Module>