using System;
using System.Collections.Generic;
using TaleWorlds.MountAndBlade;
using TOR_Core.AbilitySystem;
using LOL_Core.HeroSkillSystem.Skills.Nasus;

namespace LOL_Core.HeroSkillSystem.Core
{
    public static class HeroAbilityFactory
    {
        private static readonly Dictionary<string, HeroAbilityTemplate> _templates = new Dictionary<string, HeroAbilityTemplate>();
        private static bool _templatesLoaded = false;

        public static void LoadTemplates()
        {
            if (_templatesLoaded) return;

            LoadNasusSkills();
            
            _templatesLoaded = true;
        }

        private static void LoadNasusSkills()
        {
            var siphoningStrikeTemplate = new HeroAbilityTemplate("nasus_siphoning_strike")
            {
                Name = "死亡汲取",
                SpriteName = "nasus_q_icon",
                BaseDamage = 50f,
                CanGrow = true,
                GrowthPerKill = 1f,
                CoolDown = 8,
                Duration = 2f,
                RequiredLevel = 1,
                Description = "内瑟斯挥舞武器进行一次强力攻击，击败敌人永久增加伤害",
                HeroID = "nasus",
                SkillType = HeroSkillType.Active,
                Range = 2f,
                RequiresTarget = true,
                AbilityEffectType = AbilityEffectType.CareerAbilityEffect,
                AbilityTargetType = AbilityTargetType.SingleEnemy,
                CastType = CastType.Instant,
                AnimationActionName = "act_strike_bent_over",
                SoundEffectToPlay = "nasus_siphoning_strike",
                HasLight = true,
                LightIntensity = 2f,
                LightRadius = 3f,
                LightColorRGB = new TaleWorlds.Library.Vec3(138, 43, 226),
                ParticleEffectPrefab = "siphoning_strike_effect",
                TooltipDescription = "使用此技能击败敌人可永久提升伤害"
            };

            _templates["nasus_siphoning_strike"] = siphoningStrikeTemplate;
        }

        public static HeroAbility CreateHeroAbility(string skillID, Agent agent)
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            if (!_templates.TryGetValue(skillID, out var template))
            {
                return null;
            }

            // 使用传统的 switch 语句替代递归模式
            switch (skillID)
            {
                case "nasus_siphoning_strike":
                    return new SiphoningStrikeAbility(template);
                default:
                    return null;
            }
        }

        public static HeroAbilityTemplate GetTemplate(string skillID)
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            _templates.TryGetValue(skillID, out var template);
            return template;
        }

        public static List<string> GetAllSkillIDs()
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            return new List<string>(_templates.Keys);
        }

        public static List<HeroAbilityTemplate> GetAllTemplates()
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            return new List<HeroAbilityTemplate>(_templates.Values);
        }

        public static List<string> GetSkillsForHero(string heroID)
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            var skills = new List<string>();
            foreach (var kvp in _templates)
            {
                if (kvp.Value.HeroID == heroID || string.IsNullOrEmpty(kvp.Value.HeroID))
                {
                    skills.Add(kvp.Key);
                }
            }
            return skills;
        }

        public static bool IsValidSkillID(string skillID)
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            return _templates.ContainsKey(skillID);
        }

        public static void RegisterCustomSkill(string skillID, HeroAbilityTemplate template)
        {
            if (!_templatesLoaded)
            {
                LoadTemplates();
            }

            _templates[skillID] = template;
        }
    }
}
