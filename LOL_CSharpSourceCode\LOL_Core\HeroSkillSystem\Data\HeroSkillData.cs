using System;
using System.Collections.Generic;
using System.Xml.Serialization;
using System.Linq;

namespace LOL_Core.HeroSkillSystem.Data
{
    [Serializable]
    public class HeroSkillData
    {
        [XmlAttribute]
        public string HeroID { get; set; } = "";
        
        [XmlAttribute]
        public string EquippedSkillID { get; set; } = "";
        
        [XmlElement("SkillGrowth")]
        public List<SkillGrowthData> SkillGrowthList { get; set; } = new List<SkillGrowthData>();
        
        [XmlElement("UnlockedSkill")]
        public List<string> UnlockedSkills { get; set; } = new List<string>();
        
        [XmlAttribute]
        public DateTime LastSaved { get; set; } = DateTime.UtcNow;
        
        [XmlAttribute]
        public int TotalSkillExperience { get; set; } = 0;

        public HeroSkillData()
        {
        }

        public HeroSkillData(string heroId)
        {
            HeroID = heroId;
            EquippedSkillID = "";
            SkillGrowthList = new List<SkillGrowthData>();
            UnlockedSkills = new List<string>();
            LastSaved = DateTime.UtcNow;
            TotalSkillExperience = 0;
        }

        public void EquipSkill(string skillID)
        {
            if (UnlockedSkills.Contains(skillID))
            {
                EquippedSkillID = skillID;
                LastSaved = DateTime.UtcNow;
            }
        }

        public void UnequipSkill()
        {
            EquippedSkillID = "";
            LastSaved = DateTime.UtcNow;
        }

        public void UnlockSkill(string skillID)
        {
            if (!UnlockedSkills.Contains(skillID))
            {
                UnlockedSkills.Add(skillID);
                LastSaved = DateTime.UtcNow;
            }
        }

        public bool IsSkillUnlocked(string skillID)
        {
            return UnlockedSkills.Contains(skillID);
        }

        public bool HasEquippedSkill()
        {
            return !string.IsNullOrEmpty(EquippedSkillID);
        }

        public SkillGrowthData GetSkillGrowthData(string skillID)
        {
            return SkillGrowthList.FirstOrDefault(x => x.SkillID == skillID);
        }

        public void UpdateSkillGrowth(SkillGrowthData growthData)
        {
            var existing = SkillGrowthList.FirstOrDefault(x => x.SkillID == growthData.SkillID);
            if (existing != null)
            {
                existing.CurrentStacks = growthData.CurrentStacks;
                existing.TotalKills = growthData.TotalKills;
                existing.BonusDamage = growthData.BonusDamage;
                existing.ExperienceGained = growthData.ExperienceGained;
                existing.LastUpdated = DateTime.UtcNow;
            }
            else
            {
                SkillGrowthList.Add(growthData.Clone());
            }
            
            TotalSkillExperience = SkillGrowthList.Sum(x => x.ExperienceGained);
            LastSaved = DateTime.UtcNow;
        }

        public void ResetSkillGrowth(string skillID)
        {
            var skillData = SkillGrowthList.FirstOrDefault(x => x.SkillID == skillID);
            if (skillData != null)
            {
                skillData.Reset();
                TotalSkillExperience = SkillGrowthList.Sum(x => x.ExperienceGained);
                LastSaved = DateTime.UtcNow;
            }
        }

        public void ResetAllSkills()
        {
            foreach (var skill in SkillGrowthList)
            {
                skill.Reset();
            }
            TotalSkillExperience = 0;
            LastSaved = DateTime.UtcNow;
        }

        public int GetTotalKillsForSkill(string skillID)
        {
            var skillData = GetSkillGrowthData(skillID);
            return skillData?.TotalKills ?? 0;
        }

        public int GetCurrentStacksForSkill(string skillID)
        {
            var skillData = GetSkillGrowthData(skillID);
            return skillData?.CurrentStacks ?? 0;
        }
    }
}
