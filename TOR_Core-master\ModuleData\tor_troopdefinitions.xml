﻿<?xml version="1.0" encoding="UTF-8"?>
<NPCCharacters>
  <NPCCharacter id="tor_empire_villager" default_group="infantry" level="1" name="{=str_tor_empire_villager}Empire Villager" occupation="Villager" culture="Culture.empire" skill_template="SkillSet.tor_skills_level1">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_recruit" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_villager_template" />
      <EquipmentSet id="tor_empire_villager_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_recruit" default_group="infantry" level="6" name="{=str_tor_empire_recruit}Empire Recruit" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_state_troop" />
      <upgrade_target id="NPCCharacter.tor_empire_scout" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_recruit_template" />
      <EquipmentSet id="tor_empire_recruit_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_state_troop" default_group="infantry" level="11" name="{=str_tor_empire_state_troop}Empire State Troop" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_spearman" />
      <upgrade_target id="NPCCharacter.tor_empire_swordsman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_state_troop_template" />
      <EquipmentSet id="tor_empire_state_troop_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_swordsman" default_group="infantry" level="16" name="{=str_tor_empire_swordsman}Empire Swordsman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_veteran_swordsman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_swordsman_template" />
      <EquipmentSet id="tor_empire_swordsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_spearman" default_group="infantry" level="16" name="{=str_tor_empire_spearman}Empire Spearman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_armored_spearman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_spearman_template" />
      <EquipmentSet id="tor_empire_spearman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_armored_spearman" default_group="infantry" level="21" name="{=str_tor_empire_armored_spearman}Empire Armoured Spearman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_halberdier" />
      <upgrade_target id="NPCCharacter.tor_empire_pikeman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_armored_spearman_template" />
      <EquipmentSet id="tor_empire_armored_spearman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_veteran_swordsman" default_group="infantry" level="21" name="{=str_tor_empire_veteran_swordsman}Empire Veteran Swordsman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_greatsword" />
      <upgrade_target id="NPCCharacter.tor_empire_sergeant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_veteran_swordsman_template" />
      <EquipmentSet id="tor_empire_veteran_swordsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_halberdier" default_group="infantry" level="26" name="{=str_tor_empire_halberdier}Empire Halberdier" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_halberdier_template" />
      <EquipmentSet id="tor_empire_halberdier_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_greatsword" default_group="infantry" level="26" name="{=str_tor_empire_greatsword}Empire Greatsword" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_greatsword_template" />
      <EquipmentSet id="tor_empire_greatsword_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_pikeman" default_group="infantry" level="26" name="{=str_tor_empire_pikeman}Empire Pikeman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_pikeman_template" />
      <EquipmentSet id="tor_empire_pikeman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_sergeant" default_group="infantry" level="26" name="{=str_tor_empire_sergeant}Empire Sergeant" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_sergeant_template" />
      <EquipmentSet id="tor_empire_sergeant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_scout" default_group="ranged" level="11" name="{=str_tor_empire_scout}Empire Scout" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_skirmisher" />
      <upgrade_target id="NPCCharacter.tor_empire_archer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_scout_template" />
      <EquipmentSet id="tor_empire_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_archer" default_group="ranged" level="16" name="{=str_tor_empire_archer}Empire Archer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_longbowman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_archer_template" />
      <EquipmentSet id="tor_empire_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_skirmisher" default_group="ranged" level="16" name="{=str_tor_empire_skirmisher}Empire Skirmisher" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_crossbowman" />
      <upgrade_target id="NPCCharacter.tor_empire_handgunner" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_skirmisher_template" />
      <EquipmentSet id="tor_empire_skirmisher_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_handgunner" default_group="ranged" level="21" name="{=str_tor_empire_handgunner}Empire Handgunner" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_marksman_handgunner" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_handgunner_template" />
      <EquipmentSet id="tor_empire_handgunner_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_crossbowman" default_group="ranged" level="21" name="{=str_tor_empire_crossbowman}Empire Crossbowman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_marksman_crossbowman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_crossbowman_template" />
      <EquipmentSet id="tor_empire_crossbowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_longbowman" default_group="ranged" level="21" name="{=str_tor_empire_longbowman}Empire Longbowman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_huntsman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_longbow_template" />
      <EquipmentSet id="tor_empire_longbow_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_huntsman" default_group="ranged" level="26" name="{=str_tor_empire_huntsman}Empire Huntsman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_huntsman_template" />
      <EquipmentSet id="tor_empire_huntsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_marksman_crossbowman" default_group="ranged" level="26" name="{=str_tor_empire_marksman_crossbowman}Empire Marksman Crossbowman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_marksman_crossbowman_template" />
      <EquipmentSet id="tor_empire_marksman_crossbowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_marksman_handgunner" default_group="ranged" level="26" name="{=str_tor_empire_marksman_handgunner}Empire Marksman Handgunner" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_marksman_handgunner_template" />
      <EquipmentSet id="tor_empire_marksman_handgunner_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_young_noble_squire" default_group="infantry" level="16" name="{=str_tor_empire_young_noble_squire}Empire Young Noble Squire" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_noble_cavalry" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_young_noble_squire_template" />
      <EquipmentSet id="tor_empire_young_noble_squire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_noble_cavalry" default_group="cavalry" level="21" name="{=str_tor_empire_noble_cavalry}Empire Noble Cavalry" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_reiksguard_knight" />
      <upgrade_target id="NPCCharacter.tor_empire_pistolier" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_noble_cavalry_template" />
      <EquipmentSet id="tor_empire_noble_cavalry_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_reiksguard_knight" default_group="cavalry" level="26" name="{=str_tor_empire_reiksguard_knight}Empire Reiksguard Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_reiksguard_preceptor" />
      <upgrade_target id="NPCCharacter.tor_demigryph_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_reiksguard_knight_template" />
      <EquipmentSet id="tor_empire_reiksguard_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_pistolier" default_group="horsearcher" level="26" name="{=str_tor_empire_pistolier}Empire Pistolier" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_outrider" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_pistolier_template" />
      <EquipmentSet id="tor_empire_pistolier_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_outrider" default_group="horsearcher" level="31" name="{=str_tor_empire_outrider}Empire Outrider" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_valiant_outrider" />
      <upgrade_target id="NPCCharacter.tor_valiant_outrider_grenade" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_outrider_template" />
      <EquipmentSet id="tor_empire_outrider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_reiksguard_preceptor" default_group="cavalry" level="31" name="{=str_tor_empire_reiksguard_preceptor}Empire Reiksguard Preceptor Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_innercircle_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_reiksguard_preceptor_knight_template" />
      <EquipmentSet id="tor_empire_reiksguard_preceptor_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_innercircle_knight" default_group="cavalry" level="36" name="{=str_tor_empire_innercircle_knight}Empire Knight of the Inner Circle" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_reiksguard_preceptor_innercircle" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_reiksguard_innercircle_knight_template" />
      <EquipmentSet id="tor_empire_reiksguard_innercircle_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_demigryph_knight" default_group="cavalry" level="36" name="{=str_tor_demigryph_knight}Empire Demigryph Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_demigryph_innercircle" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_reiksguard_demigryph_knight_template" />
      <EquipmentSet id="tor_empire_reiksguard_demigryph_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_valiant_outrider" default_group="horsearcher" level="46" name="{=str_tor_valiant_outrider}Empire Valiant Outrider" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_valiant_outrider_template" />
      <EquipmentSet id="tor_empire_valiant_outrider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_valiant_outrider_grenade" default_group="horsearcher" level="46" name="{=str_tor_valiant_outrider_grenade}Empire Valiant Outrider (Grenade Launcher)" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_valiant_outrider_grenade_template" />
      <EquipmentSet id="tor_empire_valiant_outrider_grenade_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_reiksguard_preceptor_innercircle" default_group="cavalry" level="46" name="{=str_tor_reiksguard_preceptor_innercircle}Empire Reiksguard Preceptor Knight of the Inner Circle" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_reiksguard_preceptor_innercircle_template" />
      <EquipmentSet id="tor_empire_reiksguard_preceptor_innercircle_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_demigryph_innercircle" default_group="cavalry" level="46" name="{=str_tor_demigryph_innercircle}Empire Reiksguard Demigryph of the Inner Circle" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_reiksguard_demigryph_innercircle_template" />
      <EquipmentSet id="tor_empire_reiksguard_demigryph_innercircle_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_novice_engineer" default_group="infantry" level="6" name="{=str_tor_empire_novice_engineer}Novice Engineer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_trainee_artillery_crew" />
      <upgrade_target id="NPCCharacter.tor_empire_apprentice_engineer" />
      <upgrade_target id="NPCCharacter.tor_empire_iron_company_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_novice_engineer_template" />
      <EquipmentSet id="tor_empire_novice_engineer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_trainee_artillery_crew" default_group="infantry" level="16" name="{=str_tor_empire_trainee_artillery_crew}Trainee Artillery Crewman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_experienced_artillery_crew" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_trainee_artillery_crew_template" />
      <EquipmentSet id="tor_empire_trainee_artillery_crew_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_apprentice_engineer" default_group="ranged" level="16" name="{=str_tor_empire_apprentice_engineer}Apprentice Engineer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_associate_engineer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_apprentice_engineer_template" />
      <EquipmentSet id="tor_empire_apprentice_engineer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_iron_company_guard" default_group="ranged" level="16" name="{=str_tor_empire_iron_company_guard}Iron Company Guard" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_iron_company_troop" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_iron_company_guard_template" />
      <EquipmentSet id="tor_empire_iron_company_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_experienced_artillery_crew" default_group="infantry" level="21" name="{=str_tor_empire_experienced_artillery_crew}Experienced Artillery Crew" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_veteran_artillery_crew" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_experienced_artillery_crew_template" />
      <EquipmentSet id="tor_empire_experienced_artillery_crew_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_associate_engineer" default_group="ranged" level="21" name="{=str_tor_empire_associate_engineer}Associate Engineer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_master_engineer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_associate_engineer_template" />
      <EquipmentSet id="tor_empire_associate_engineer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_iron_company_troop" default_group="ranged" level="21" name="{=str_tor_empire_iron_company_troop}Iron Company Troop" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_ironsider" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_iron_company_troop_template" />
      <EquipmentSet id="tor_empire_iron_company_troop_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_veteran_artillery_crew" default_group="infantry" level="26" name="{=str_tor_empire_veteran_artillery_crew}Veteran Artillery Crew" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_veteran_artillery_crew_template" />
      <EquipmentSet id="tor_empire_veteran_artillery_crew_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_master_engineer" default_group="ranged" level="26" name="{=str_tor_empire_master_engineer}Master Engineer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_master_engineer_template" />
      <EquipmentSet id="tor_empire_master_engineer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_ironsider" default_group="ranged" level="26" name="{=str_tor_empire_ironsider}Nuln Ironsider" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_ironsider_template" />
      <EquipmentSet id="tor_empire_ironsider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_caravan_captain" default_group="cavalry" level="26" name="{=str_tor_empire_caravan_captain}Empire Caravan Captain" occupation="CaravanGuard" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_captain_template" />
      <EquipmentSet id="tor_empire_caravan_captain_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_caravan_guard" default_group="cavalry" level="21" name="{=str_tor_empire_caravan_guard}Empire Caravan Guard" occupation="CaravanGuard" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_guard_template" />
      <EquipmentSet id="tor_empire_caravan_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_caravan_vetguard" default_group="cavalry" level="26" name="{=str_tor_empire_caravan_vetguard}Empire Knight" occupation="CaravanGuard" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_vetguard_template" />
      <EquipmentSet id="tor_empire_caravan_vetguard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_empire_caravan_ranged}Empire Crossbowman" occupation="CaravanGuard" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_ranged_template" />
      <EquipmentSet id="tor_empire_caravan_ranged_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_flagellant" default_group="infantry" level="16" name="{=str_tor_empire_flagellant}Empire Flagellant" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.flagellant" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_flagellant_template" />
      <EquipmentSet id="tor_empire_flagellant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_wolfkin" default_group="infantry" level="16" name="{=str_tor_empire_wolfkin}Wolf Kin" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_wolfbrother" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_wolf_kin_template" />
      <EquipmentSet id="tor_wolf_kin_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_wolfbrother" default_group="infantry" level="21" name="{=str_tor_empire_wolfbrother}Wolf Brother" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_warrior_ulric" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_wolf_brother_template" />
      <EquipmentSet id="tor_wolf_brother_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_warrior_ulric" default_group="infantry" level="26" name="{=str_tor_empire_warrior_ulric}Warrior of Ulric" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_teutogen_guard" />
      <upgrade_target id="NPCCharacter.tor_empire_whitewolf_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_warrior_of_ulric_template" />
      <EquipmentSet id="tor_warrior_of_ulric_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_teutogen_guard" default_group="infantry" level="41" name="{=str_tor_empire_teutogen_guard}Teutogen Guard" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_teutogen_guard_template" />
      <EquipmentSet id="tor_empire_teutogen_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_whitewolf_knight" default_group="cavalry" level="41" name="{=str_tor_empire_whitewolf_knight}Knight of the White Wolf" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <beard_tags>
        <beard_tag name="LongScragglyBeard" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_knight_of_the_whitewolf_template" />
      <EquipmentSet id="tor_empire_knight_of_the_whitewolf_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_villager" default_group="infantry" level="1" name="{=str_tor_br_villager}Bretonnian Villager" occupation="Villager" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level1">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_peasant_levy" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_villager_template" />
      <EquipmentSet id="tor_br_villager_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_battle_pilgrim" default_group="infantry" level="16" name="{=str_tor_br_battle_pilgrim}Battle Pilgrim" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_battle_pilgrim_template" />
      <EquipmentSet id="tor_br_battle_pilgrim_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_peasant_levy" default_group="infantry" level="6" name="{=str_tor_br_peasant_levy}Bretonnian Peasant Levy" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_peasant_archer" />
      <upgrade_target id="NPCCharacter.tor_br_manatarms_militia" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_peasant_levy_template" />
      <EquipmentSet id="tor_br_peasant_levy_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_peasant_archer" default_group="ranged" level="11" name="{=str_tor_br_peasant_archer}Bretonnian Peasant Archer" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_longbowman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_peasant_archer_template" />
      <EquipmentSet id="tor_br_peasant_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_longbowman" default_group="ranged" level="16" name="{=str_tor_br_longbowman}Bretonnian Longbowman" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_villein" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_hunter_template" />
      <EquipmentSet id="tor_br_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_villein" default_group="ranged" level="21" name="{=str_tor_br_villein}Bretonnian Villein" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_villein_template" />
      <EquipmentSet id="tor_br_villein_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_manatarms_militia" default_group="infantry" level="11" name="{=str_tor_br_manatarms_militia}Bretonnian Man-at-Arms militiaman" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_manatarms" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_militia_template" />
      <EquipmentSet id="tor_br_manatarms_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_manatarms" default_group="infantry" level="16" name="{=str_tor_br_manatarms}Bretonnian Man-at-Arms" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_manatarms_halberd" />
      <upgrade_target id="NPCCharacter.tor_br_yeoman" />
      <upgrade_target id="NPCCharacter.tor_br_mounted_yeoman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_template" />
      <EquipmentSet id="tor_br_manatarms_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_manatarms_halberd" default_group="infantry" level="21" name="{=str_tor_br_manatarms_halberd}Bretonnian Man-at-Arms Billman" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_billman_template" />
      <EquipmentSet id="tor_br_manatarms_billman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_yeoman" default_group="infantry" level="21" name="{=str_tor_br_yeoman}Bretonnian Yeoman" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_yeoman_warden" />
      <upgrade_target id="NPCCharacter.tor_br_foot_squire" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_yeoman_template" />
      <EquipmentSet id="tor_br_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_yeoman_warden" default_group="infantry" level="26" name="{=str_tor_br_yeoman_warden}Bretonnian Yeoman Warden" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_yeoman_warden_template" />
      <EquipmentSet id="tor_br_yeoman_warden_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_foot_squire" default_group="infantry" level="26" name="{=str_tor_br_foot_squire}Bretonnian Foot Squire" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_foot_squire_template" />
      <EquipmentSet id="tor_br_foot_squire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_mounted_yeoman" default_group="cavalry" level="21" name="{=str_tor_br_mounted_yeoman}Bretonnian Mounted Yeoman" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_mounted_warden" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_mounted_yeoman_template" />
      <EquipmentSet id="tor_br_mounted_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_mounted_warden" default_group="cavalry" level="26" name="{=str_tor_br_mounted_warden}Bretonnian Mounted Yeoman Warden" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_mounted_yeoman_warden_template" />
      <EquipmentSet id="tor_br_mounted_yeoman_warden_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_noble" default_group="cavalry" level="11" name="{=str_tor_br_noble}Bretonnian Noble" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_knight_errant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_noble_template" />
      <EquipmentSet id="tor_br_noble_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_knight_errant" default_group="cavalry" level="21" name="{=str_tor_br_knight_errant}Bretonnian Knight Errant" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_realm_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_knight_errant_template" />
      <EquipmentSet id="tor_br_knight_errant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_realm_knight" default_group="cavalry" level="31" name="{=str_tor_br_realm_knight}Bretonnian Knight of the Realm" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_quest_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_realm_knight_template" />
      <EquipmentSet id="tor_br_realm_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_quest_knight" default_group="cavalry" level="41" name="{=str_tor_br_quest_knight}Bretonnian Questing Knight" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_grail_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_quest_knight_template" />
      <EquipmentSet id="tor_br_quest_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_grail_knight" default_group="cavalry" level="46" name="{=str_tor_br_grail_knight}Bretonnian Grail Knight" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_grail_knight_template" />
      <EquipmentSet id="tor_br_grail_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_caravan_guard" default_group="cavalry" level="21" name="{=str_tor_br_caravan_guard}Bretonnian Caravan Horseman" occupation="CaravanGuard" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_caravan_captain" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_mounted_yeoman_template" />
      <EquipmentSet id="tor_br_mounted_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_br_caravan_ranged}Bretonnian Caravan Bowman" occupation="CaravanGuard" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_caravan_elite_ranged" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_peasant_archer_template" />
      <EquipmentSet id="tor_br_peasant_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_caravan_elite_ranged" default_group="ranged" level="26" name="{=str_tor_br_caravan_elite_ranged}Bretonnian Caravan Longbowman" occupation="CaravanGuard" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_hunter_template" />
      <EquipmentSet id="tor_br_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_caravan_captain" default_group="infantry" level="26" name="{=str_tor_br_caravan_captain}Bretonnian Caravan Master" occupation="CaravanGuard" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_template" />
      <EquipmentSet id="tor_br_manatarms_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_villager" default_group="infantry" level="1" name="{=str_tor_m_villager}Mousillon Villager" occupation="Villager" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level1">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_peasant_levy" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_villager_template" />
      <EquipmentSet id="tor_br_villager_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_peasant_levy" default_group="infantry" level="6" name="{=str_tor_m_peasant_levy}Mousillon Peasant Levy" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_peasant_archer" />
      <upgrade_target id="NPCCharacter.tor_m_manatarms_militia" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_peasant_levy_template" />
      <EquipmentSet id="tor_br_peasant_levy_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_peasant_archer" default_group="ranged" level="11" name="{=str_tor_m_peasant_archer}Mousillon Peasant Archer" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_longbowman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_peasant_archer_template" />
      <EquipmentSet id="tor_br_peasant_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_longbowman" default_group="ranged" level="16" name="{=str_tor_m_longbowman}Mousillon Longbowman" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_villein" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_hunter_template" />
      <EquipmentSet id="tor_br_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_villein" default_group="ranged" level="21" name="{=str_tor_m_villein}Mousillon Villein" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_villein_template" />
      <EquipmentSet id="tor_br_villein_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_manatarms_militia" default_group="infantry" level="11" name="{=str_tor_m_manatarms_militia}Mousillon Man-at-Arms militiaman" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_manatarms" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_militia_template" />
      <EquipmentSet id="tor_br_manatarms_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_manatarms" default_group="infantry" level="16" name="{=str_tor_m_manatarms}Mousillon Man-at-Arms" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_manatarms_halberd" />
      <upgrade_target id="NPCCharacter.tor_m_yeoman" />
      <upgrade_target id="NPCCharacter.tor_m_mounted_yeoman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_template" />
      <EquipmentSet id="tor_br_manatarms_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_manatarms_halberd" default_group="infantry" level="21" name="{=str_tor_m_manatarms_halberd}Mousillon Man-at-Arms Billman" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_billman_template" />
      <EquipmentSet id="tor_br_manatarms_billman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_yeoman" default_group="infantry" level="21" name="{=str_tor_m_yeoman}Mousillon Yeoman" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_yeoman_template" />
      <EquipmentSet id="tor_br_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_mounted_yeoman" default_group="cavalry" level="26" name="{=str_tor_m_mounted_yeoman}Mousillon Mounted Yeoman" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_mounted_yeoman_template" />
      <EquipmentSet id="tor_br_mounted_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_illfated_squire" default_group="infantry" level="11" name="{=str_tor_m_illfated_squire}Mousillon Ill-fated Squire" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_outcast_errant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_foot_squire_template" />
      <EquipmentSet id="tor_br_foot_squire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_outcast_errant" default_group="cavalry" level="21" name="{=str_tor_m_outcast_errant}Mousillon Outcast Errant" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_knight_of_misfortune" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_knight_errant_template" />
      <EquipmentSet id="tor_br_knight_errant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_knight_of_misfortune" default_group="cavalry" level="31" name="{=str_tor_m_knight_of_misfortune}Mousillon Knight of Misfortune" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_doomed_questing_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_m_knight_of_misfortune_template" />
      <EquipmentSet id="tor_m_knight_of_misfortune_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_doomed_questing_knight" default_group="cavalry" level="41" name="{=str_tor_m_doomed_questing_knight}Mousillon Doomed Questing Knight" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_knight_of_the_black_grail" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_m_doomed_questing_knight_template" />
      <EquipmentSet id="tor_m_doomed_questing_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_knight_of_the_black_grail" default_group="cavalry" level="46" name="{=str_tor_m_knight_of_the_black_grail}Mousillon Knight of the Black Grail" occupation="Soldier" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_m_black_grail_knight_template" />
      <EquipmentSet id="tor_m_black_grail_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_caravan_guard" default_group="cavalry" level="21" name="{=str_tor_m_caravan_guard}Mousillon Caravan Horseman" occupation="CaravanGuard" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_caravan_captain" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_mounted_yeoman_template" />
      <EquipmentSet id="tor_br_mounted_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_m_caravan_ranged}Mousillon Caravan Bowman" occupation="CaravanGuard" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_m_caravan_elite_ranged" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_peasant_archer_template" />
      <EquipmentSet id="tor_br_peasant_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_caravan_elite_ranged" default_group="ranged" level="26" name="{=str_tor_m_caravan_elite_ranged}Mousillon Caravan Longbowman" occupation="CaravanGuard" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_hunter_template" />
      <EquipmentSet id="tor_br_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_m_caravan_captain" default_group="infantry" level="26" name="{=str_tor_m_caravan_captain}Mousillon Caravan Master" occupation="CaravanGuard" culture="Culture.mousillon" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_template" />
      <EquipmentSet id="tor_br_manatarms_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_servant" default_group="infantry" level="1" name="{=str_tor_we_servant}Asrai Servant" occupation="Villager" culture="Culture.battania" skill_template="SkillSet.tor_skills_level1" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_militia" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_servant_template" />
      <EquipmentSet id="tor_we_servant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_militia" default_group="infantry" level="6" name="{=str_tor_we_militia}Asrai Militia" occupation="Villager" culture="Culture.battania" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_eternal_guard" />
      <upgrade_target id="NPCCharacter.tor_we_glade_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_militia_template" />
      <EquipmentSet id="tor_we_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_glade_guard" default_group="ranged" level="11" name="{=str_tor_we_glade_guard}Asrai Glade Guard" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level11" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_deepwood_scout" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_glade_guard_template" />
      <EquipmentSet id="tor_we_glade_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_eternal_guard" default_group="infantry" level="11" name="{=str_tor_we_eternal_guard}Asrai Eternal Guard" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level11" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_glade_rider" />
      <upgrade_target id="NPCCharacter.tor_we_eternal_warden" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_eternal_guard_template" />
      <EquipmentSet id="tor_we_eternal_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_deepwood_scout" default_group="ranged" level="16" name="{=str_tor_we_deepwood_scout}Asrai Deepwood Scout" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_master_scout" />
      <upgrade_target id="NPCCharacter.tor_we_waywatcher" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_deepwood_scout_template" />
      <EquipmentSet id="tor_we_deepwood_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_glade_rider" default_group="horsearcher" level="16" name="{=str_tor_we_glade_rider}Asrai Glade Rider" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_glade_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_glade_rider_template" />
      <EquipmentSet id="tor_we_glade_rider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_eternal_warden" default_group="infantry" level="16" name="{=str_tor_we_eternal_warden}Asrai Eternal Warden" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_eternal_warden_template" />
      <EquipmentSet id="tor_we_eternal_warden_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_master_scout" default_group="ranged" level="21" name="{=str_tor_we_master_scout}Asrai Master Scout" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_master_scout_template" />
      <EquipmentSet id="tor_we_master_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_waywatcher" default_group="ranged" level="21" name="{=str_tor_we_waywatcher}Asrai Waywatcher" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_waywatcher_sentinel" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_waywatcher_template" />
      <EquipmentSet id="tor_we_waywatcher_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_glade_knight" default_group="cavalry" level="21" name="{=str_tor_we_glade_knight}Asrai Glade Knight" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_glade_knight_template" />
      <EquipmentSet id="tor_we_glade_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_waywatcher_sentinel" default_group="ranged" level="26" name="{=str_tor_we_waywatcher_sentinel}Asrai Waywatcher Sentinel" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_waywatcher_sentinel_template" />
      <EquipmentSet id="tor_we_waywatcher_sentinel_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_wardancer" default_group="infantry" level="16" name="{=str_tor_we_wardancer}Asrai Wardancer" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true" is_female="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.female_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_shadowdancer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_wardancer_template" />
      <EquipmentSet id="tor_we_wardancer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_shadowdancer" default_group="infantry" level="21" name="{=str_tor_we_shadowdancer}Asrai Shadowdancer" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level21" is_female="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.female_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_bladesinger" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_shadowdancer_template" />
      <EquipmentSet id="tor_we_shadowdancer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_bladesinger" default_group="infantry" level="26" name="{=str_tor_we_bladesinger}Asrai Bladesinger" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level26" is_female="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.female_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_bladesinger_template" />
      <EquipmentSet id="tor_we_bladesinger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_wildwood_ranger" default_group="infantry" level="16" name="{=str_tor_we_wildwood_ranger}Cythrali Wildwood Ranger" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_wildwood_warden" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_wildwood_ranger_template" />
      <EquipmentSet id="tor_we_wildwood_ranger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_wildwood_warden" default_group="infantry" level="26" name="{=str_tor_we_wildwood_warden}Cythrali Wildwood Warden" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_wildwood_warden_template" />
      <EquipmentSet id="tor_we_wildwood_warden_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_wild_rider" default_group="cavalry" level="31" name="{=str_tor_we_wild_rider}Wild Rider" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level31" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_great_stag_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_wild_rider_template" />
      <EquipmentSet id="tor_we_wild_rider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_great_stag_knight" default_group="cavalry" level="36" name="{=str_tor_we_great_stag_knight}Great Stag Knight" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level36" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_great_stag_knight_template" />
      <EquipmentSet id="tor_we_great_stag_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_dryad" default_group="infantry" level="41" name="{=str_tor_we_dryad}Dryad" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level41" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="Bald" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_dryad_template" />
      <EquipmentSet id="tor_we_dryad_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_treeman" default_group="infantry" level="41" name="{=str_tor_we_treeman}Treeman" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level41" is_basic_troop="true" race="large_humanoid_monster">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="Bald" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_ancient_treeman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_treeman_template" />
      <EquipmentSet id="tor_we_treeman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_ancient_treeman" default_group="infantry" level="46" name="{=str_tor_we_ancient_treeman}Ancient Treeman" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level46" race="large_humanoid_monster">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
      <hair_tags>
        <hair_tag name="Bald" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_ancient_treeman_template" />
      <EquipmentSet id="tor_we_ancient_treeman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_caravan_guard" default_group="infantry" level="21" name="{=str_tor_we_caravan_guard}Asrai Trader" occupation="CaravanGuard" culture="Culture.battania" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_caravan_captain" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_caravan_guard_template" />
      <EquipmentSet id="tor_we_caravan_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_we_caravan_ranged}Asrai Trader Guard" occupation="CaravanGuard" culture="Culture.battania" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_we_caravan_elite_ranged" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_we_caravan_ranged_template" />
      <EquipmentSet id="tor_we_caravan_ranged_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_caravan_elite_ranged" default_group="ranged" level="26" name="{=str_tor_we_caravan_elite_ranged}Asrai Elite Trader Guard" occupation="CaravanGuard" culture="Culture.battania" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_caravan_ranged_elite_template" />
      <EquipmentSet id="tor_we_caravan_ranged_elite_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_we_caravan_captain" default_group="infantry" level="26" name="{=str_tor_we_caravan_captain}Asrai Master Trader" occupation="CaravanGuard" culture="Culture.battania" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_we_caravan_captain_template" />
      <EquipmentSet id="tor_we_caravan_captain_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_forestborn" default_group="infantry" level="1" name="{=str_tor_eo_forestborn}Eonir Forestborn" occupation="Villager" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level1" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_militia" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_forestborn_template" />
      <EquipmentSet id="tor_eo_forestborn_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_militia" default_group="infantry" level="6" name="{=str_tor_eo_militia}Eonir Forestborn Militia" occupation="Villager" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_kithband_aspirant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_militia_template" />
      <EquipmentSet id="tor_eo_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_kithband_aspirant" default_group="infantry" level="11" name="{=str_tor_eo_kithband_aspirant}Eonir Kithband Aspirant" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level11" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_kithband_warrior" />
      <upgrade_target id="NPCCharacter.tor_eo_kithband_sentinel" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_kithband_aspirant_template" />
      <EquipmentSet id="tor_eo_kithband_aspirant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_kithband_warrior" default_group="infantry" level="16" name="{=str_tor_eo_kithband_warrior}Eonir Kithband Warrior" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_mounted_kithband_warrior" />
      <upgrade_target id="NPCCharacter.tor_eo_armoured_kithband_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_kithband_warrior_template" />
      <EquipmentSet id="tor_eo_kithband_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_mounted_kithband_warrior" default_group="cavalry" level="21" name="{=str_tor_eo_mounted_kithband_warrior}Eonir Mounted Kithband Warrior" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_mounted_kithband_warrior_template" />
      <EquipmentSet id="tor_eo_mounted_kithband_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_armoured_kithband_warrior" default_group="infantry" level="21" name="{=str_tor_eo_armoured_kithband_warrior}Eonir Armored Kithband Warrior" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_veteran_kithband_warrior_sword" />
      <upgrade_target id="NPCCharacter.tor_eo_veteran_kithband_warrior_spear" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_armoured_kithband_warrior_template" />
      <EquipmentSet id="tor_eo_armoured_kithband_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_veteran_kithband_warrior_sword" default_group="infantry" level="31" name="{=str_tor_eo_veteran_kithband_warrior_sword}Eonir Veteran Kithband Warrior (Sword)" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_veteran_kithband_warrior_sword_template" />
      <EquipmentSet id="tor_eo_veteran_kithband_warrior_sword_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_veteran_kithband_warrior_spear" default_group="infantry" level="31" name="{=str_tor_eo_veteran_kithband_warrior_spear}Eonir Veteran Kithband Warrior (Spear)" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_veteran_kithband_warrior_spear_template" />
      <EquipmentSet id="tor_eo_veteran_kithband_warrior_spear_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_kithband_sentinel" default_group="ranged" level="21" name="{=str_tor_eo_kithband_sentinel}Eonir Waywatcher" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_ranger" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_kithband_sentinel_template" />
      <EquipmentSet id="tor_eo_kithband_sentinel_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_ranger" default_group="ranged" level="31" name="{=str_tor_eo_ranger}Eonir Ranger" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_mounted_ranger" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_ranger_template" />
      <EquipmentSet id="tor_eo_ranger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_mounted_ranger" default_group="horsearcher" level="46" name="{=str_tor_eo_mounted_ranger}Eonir Mounted Ranger" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level46" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_mounted_ranger_template" />
      <EquipmentSet id="tor_eo_mounted_ranger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_cityborn_militia" default_group="infantry" level="16" name="{=str_tor_eo_cityborn_militia}Eonir Cityborn Militia" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_cityborn_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_cityborn_militia_template" />
      <EquipmentSet id="tor_eo_cityborn_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_cityborn_guard" default_group="infantry" level="26" name="{=str_tor_eo_cityborn_guard}Eonir Tor Lithanel Guard" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_cityborn_council_guard" />
      <upgrade_target id="NPCCharacter.tor_eo_cityborn_council_sentinel" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_cityborn_guard_template" />
      <EquipmentSet id="tor_eo_cityborn_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_cityborn_council_guard" default_group="infantry" level="31" name="{=str_tor_eo_cityborn_council_guard}Eonir Council Warden" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_queens_guard_sword" />
      <upgrade_target id="NPCCharacter.tor_eo_queens_guard_halberd" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_cityborn_council_guard_template" />
      <EquipmentSet id="tor_eo_cityborn_council_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_cityborn_council_sentinel" default_group="ranged" level="31" name="{=str_tor_eo_cityborn_council_sentinel}Eonir Council Sentinel" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_envoy_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_cityborn_council_sentinel_template" />
      <EquipmentSet id="tor_eo_cityborn_council_sentinel_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_queens_guard_sword" default_group="infantry" level="46" name="{=str_tor_eo_queens_guard_sword}Eonir Queen's Champion" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level46" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_queens_guard_sword_template" />
      <EquipmentSet id="tor_eo_queens_guard_sword_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_queens_guard_halberd" default_group="infantry" level="46" name="{=str_tor_eo_queens_guard_halberd}Eonir Queen's Defender" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level46" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_queens_guard_halberd_template" />
      <EquipmentSet id="tor_eo_queens_guard_halberd_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_envoy_guard" default_group="ranged" level="46" name="{=str_tor_eo_envoy_guard}Eonir Envoy Guard" occupation="Soldier" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level46" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_envoy_guard_template" />
      <EquipmentSet id="tor_eo_envoy_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_caravan_guard" default_group="infantry" level="21" name="{=str_tor_eo_caravan_guard}Eonir Trader" occupation="CaravanGuard" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_caravan_guard_template" />
      <EquipmentSet id="tor_eo_caravan_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_eo_caravan_ranged}Eonir Trader Guard" occupation="CaravanGuard" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_caravan_ranged_template" />
      <EquipmentSet id="tor_eo_caravan_ranged_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_caravan_elite_ranged" default_group="ranged" level="26" name="{=str_tor_eo_caravan_elite_ranged}Eonir Elite Trader Guard" occupation="CaravanGuard" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_caravan_elite_ranged_template" />
      <EquipmentSet id="tor_eo_caravan_elite_ranged_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_caravan_captain" default_group="infantry" level="26" name="{=str_tor_eo_caravan_captain}Eonir Master Trader" occupation="CaravanGuard" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level26" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_caravan_captain_template" />
      <EquipmentSet id="tor_eo_caravan_captain_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_thug" default_group="infantry" level="6" name="{=str_tor_eo_thug}Eonir Thug" occupation="Gangster" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_expert_thug" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_thug_template" />
      <EquipmentSet id="tor_eo_thug_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_expert_thug" default_group="infantry" level="11" name="{=str_tor_eo_expert_thug}Eonir Expert Thug" occupation="Gangster" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level11" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_eo_master_thug" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_eo_expert_thug_template" />
      <EquipmentSet id="tor_eo_expert_thug_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_eo_master_thug" default_group="infantry" level="21" name="{=str_tor_eo_master_thug}Eonir Master Thug" occupation="Gangster" culture="Culture.eonir" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_eo_master_thug_template" />
      <EquipmentSet id="tor_eo_master_thug_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_he_seaelf_militia" default_group="infantry" level="11" name="{=str_tor_he_seaelf_militia}Sea Elf Militia" occupation="Soldier" culture="Culture.asur" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_he_seaelf_archer" />
      <upgrade_target id="NPCCharacter.tor_he_seaelf_spearman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_he_seaelf_militia_template" />
      <EquipmentSet id="tor_he_seaelf_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_he_seaelf_archer" default_group="ranged" level="21" name="{=str_tor_he_seaelf_archer}Sea Elf Archer" occupation="Soldier" culture="Culture.asur" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_he_seaelf_hawkeye" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_he_seaelf_archer_template" />
      <EquipmentSet id="tor_he_seaelf_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_he_seaelf_hawkeye" default_group="ranged" level="31" name="{=str_tor_he_seaelf_hawkeye}Sea Elf Hawk Eye" occupation="Soldier" culture="Culture.asur" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_he_seaelf_hawkeye_template" />
      <EquipmentSet id="tor_he_seaelf_hawkeye_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_he_seaelf_spearman" default_group="infantry" level="21" name="{=str_tor_he_seaelf_spearman}Sea Elf Spearman" occupation="Soldier" culture="Culture.asur" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_he_seaelf_sentinel" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_he_seaelf_spearman_template" />
      <EquipmentSet id="tor_he_seaelf_spearman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_he_seaelf_sentinel" default_group="infantry" level="31" name="{=str_tor_he_seaelf_sentinel}Sea Elf Sentinel" occupation="Soldier" culture="Culture.asur" skill_template="SkillSet.tor_skills_level31" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_he_seaelf_sentinel_template" />
      <EquipmentSet id="tor_he_seaelf_sentinel_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_he_white_lion_chrace" default_group="infantry" level="46" name="{=str_tor_he_white_lion_chrace}White Lion of Chrace" occupation="Soldier" culture="Culture.asur" skill_template="SkillSet.tor_skills_level46" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_he_white_lion_chrace_template" />
      <EquipmentSet id="tor_he_white_lion_chrace_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_de_conscript" default_group="infantry" level="6" name="{=str_tor_de_conscript}Druchii Conscript" occupation="Soldier" culture="Culture.druchii" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_de_bleaksword" />
      <upgrade_target id="NPCCharacter.tor_de_dreadspear" />
      <upgrade_target id="NPCCharacter.tor_de_darkshard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_de_conscript_template" />
      <EquipmentSet id="tor_de_conscript_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_de_bleaksword" default_group="infantry" level="16" name="{=str_tor_de_bleaksword}Druchii Bleaksword" occupation="Soldier" culture="Culture.druchii" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_de_bleaksword_template" />
      <EquipmentSet id="tor_de_bleaksword_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_de_dreadspear" default_group="infantry" level="16" name="{=str_tor_de_dreadspear}Druchii Dreadspear" occupation="Soldier" culture="Culture.druchii" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_de_dreadspear_template" />
      <EquipmentSet id="tor_de_dreadspear_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_de_darkshard" default_group="ranged" level="16" name="{=str_tor_de_darkshard}Druchii Darkshard" occupation="Soldier" culture="Culture.druchii" skill_template="SkillSet.tor_skills_level16" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_de_darkrider" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_de_darkshard_template" />
      <EquipmentSet id="tor_de_darkshard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_de_darkrider" default_group="cavalry" level="21" name="{=str_tor_de_darkrider}Druchii Dark Rider" occupation="Soldier" culture="Culture.druchii" skill_template="SkillSet.tor_skills_level21" race="elf">
    <face>
      <face_key_template value="BodyProperty.male_wood_elf" />
      <hair_tags>
        <hair_tag name="ElfHair" />
      </hair_tags>
      <beard_tags>
        <beard_tag name="Cleanshaven" />
      </beard_tags>
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_de_darkrider_template" />
      <EquipmentSet id="tor_de_darkrider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_villager" default_group="infantry" level="1" name="{=str_tor_vc_villager}Sylvanian Peasant" occupation="Villager" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level1">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_recruit" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_villager_template" />
      <EquipmentSet id="tor_empire_villager_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_recruit" default_group="infantry" level="6" name="{=str_tor_vc_empire_recruit}Sylvanian Armed Peasant" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_levy" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_recruit_template" />
      <EquipmentSet id="tor_empire_recruit_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_levy" default_group="infantry" level="11" name="{=str_tor_vc_empire_levy}Sylvanian Levy" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_state_troop" />
      <upgrade_target id="NPCCharacter.tor_vc_empire_archer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_recruit_template" />
      <EquipmentSet id="tor_empire_recruit_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_state_troop" default_group="infantry" level="16" name="{=str_tor_vc_empire_state_troop}Sylvanian State Troop" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_veteran_state_troop" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_state_troop_template" />
      <EquipmentSet id="tor_empire_state_troop_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_veteran_state_troop" default_group="infantry" level="21" name="{=str_tor_vc_empire_veteran_state_troop}Sylvanian Veteran State Troop" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_sergeant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_swordsman_template" />
      <EquipmentSet id="tor_empire_swordsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_sergeant" default_group="infantry" level="26" name="{=str_tor_vc_empire_sergeant}Sylvanian Sergeant" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_veteran_swordsman_template" />
      <EquipmentSet id="tor_empire_veteran_swordsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_archer" default_group="ranged" level="16" name="{=str_tor_vc_empire_archer}Sylvanian Scout" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_hunter" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_scout_template" />
      <EquipmentSet id="tor_empire_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_hunter" default_group="ranged" level="21" name="{=str_tor_vc_empire_hunter}Sylvanian Hunter" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_empire_ranger" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_archer_template" />
      <EquipmentSet id="tor_empire_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_empire_ranger" default_group="ranged" level="26" name="{=str_tor_vc_empire_ranger}Sylvanian Marsh Ranger" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_longbow_template" />
      <EquipmentSet id="tor_empire_longbow_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_summoned_skeleton" default_group="infantry" level="1" name="{=str_tor_vc_summoned_skeleton}Summoned Skeleton" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level1" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_template" />
      <EquipmentSet id="tor_vc_skeleton_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_skeleton" default_group="infantry" level="1" name="{=str_tor_vc_skeleton}Skeleton" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level1" is_basic_troop="true" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_skeleton_swordsman" />
      <upgrade_target id="NPCCharacter.tor_vc_skeleton_spearman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_template" />
      <EquipmentSet id="tor_vc_skeleton_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_skeleton_swordsman" default_group="infantry" level="6" name="{=str_tor_vc_skeleton_swordsman}Skeleton Swordsman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level6" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_skeleton_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_swordsman_template" />
      <EquipmentSet id="tor_vc_skeleton_swordsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_skeleton_warrior" default_group="infantry" level="11" name="{=str_tor_vc_skeleton_warrior}Skeleton Warrior" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level11" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_crypt_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_warrior_template" />
      <EquipmentSet id="tor_vc_skeleton_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_crypt_warrior" default_group="infantry" level="16" name="{=str_tor_vc_crypt_warrior}Crypt Warrior" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_grave_guard_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_crypt_warrior_template" />
      <EquipmentSet id="tor_vc_crypt_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_grave_guard_warrior" default_group="infantry" level="21" name="{=str_tor_vc_grave_guard_warrior}Grave Guard Warrior" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_grave_guard_seneschal" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_grave_guard_warrior_template" />
      <EquipmentSet id="tor_vc_grave_guard_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_grave_guard_seneschal" default_group="infantry" level="31" name="{=str_tor_vc_grave_guard_seneschal}Grave Guard Seneschal" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_grave_guard_seneschal_template" />
      <EquipmentSet id="tor_vc_grave_guard_seneschal_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_skeleton_spearman" default_group="infantry" level="6" name="{=str_tor_vc_skeleton_spearman}Skeleton Spearman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level6" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_skeleton_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_spearman_template" />
      <EquipmentSet id="tor_vc_skeleton_spearman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_skeleton_guard" default_group="infantry" level="11" name="{=str_tor_vc_skeleton_guard}Skeleton Guard" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level11" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_crypt_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_guard_template" />
      <EquipmentSet id="tor_vc_skeleton_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_crypt_guard" default_group="infantry" level="16" name="{=str_tor_vc_crypt_guard}Crypt Guard" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_grave_guard_spearman" />
      <upgrade_target id="NPCCharacter.tor_vc_black_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_crypt_guard_template" />
      <EquipmentSet id="tor_vc_crypt_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_grave_guard_spearman" default_group="infantry" level="21" name="{=str_tor_vc_grave_guard_spearman}Grave Guard Spearman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_grave_guardian" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_grave_guard_spearman_template" />
      <EquipmentSet id="tor_vc_grave_guard_spearman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_grave_guardian" default_group="infantry" level="31" name="{=str_tor_vc_grave_guardian}Grave Guardian" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_grave_guardian_template" />
      <EquipmentSet id="tor_vc_grave_guardian_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_black_knight" default_group="cavalry" level="21" name="{=str_tor_vc_black_knight}Black Knight" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_hell_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_black_knight_template" />
      <EquipmentSet id="tor_vc_black_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_hell_knight" default_group="cavalry" level="31" name="{=str_tor_vc_hell_knight}Hell Knight" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_hell_knight_template" />
      <EquipmentSet id="tor_vc_hell_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_spirit_host" default_group="infantry" level="21" name="{=str_tor_vc_spirit_host}Spirit Host" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true" race="wraith">
    <face>
      <face_key_template value="BodyProperty.wraith" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_cairn_wraith" />
      <upgrade_target id="NPCCharacter.tor_vc_hex_wraith" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_spirithost_template" />
      <EquipmentSet id="tor_vc_spirithost_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_cairn_wraith" default_group="infantry" level="31" name="{=str_tor_vc_cairn_wraith}Cairn Wraith" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="wraith">
    <face>
      <face_key_template value="BodyProperty.wraith" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_crypt_wraith" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_cairn_wraith_template" />
      <EquipmentSet id="tor_vc_cairn_wraith_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_crypt_wraith" default_group="infantry" level="36" name="{=str_tor_vc_crypt_wraith}Crypt Wraith" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level36" race="wraith">
    <face>
      <face_key_template value="BodyProperty.wraith" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_crypt_wraith_template" />
      <EquipmentSet id="tor_vc_crypt_wraith_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_hex_wraith" default_group="cavalry" level="31" name="{=str_tor_vc_hex_wraith}Hex Wraith" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="wraith">
    <face>
      <face_key_template value="BodyProperty.wraith" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_hell_wraith" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_hex_wraith_template" />
      <EquipmentSet id="tor_vc_hex_wraith_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_hell_wraith" default_group="cavalry" level="36" name="{=str_tor_vc_hell_wraith}Hell Wraith" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level36" race="wraith">
    <face>
      <face_key_template value="BodyProperty.wraith" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_hell_wraith_template" />
      <EquipmentSet id="tor_vc_hell_wraith_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_con_carstein_noble" default_group="infantry" level="21" name="{=str_tor_vc_con_carstein_noble}Von Carstein Noble" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_von_carstein_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_vampire_newblood_template" />
      <EquipmentSet id="tor_vc_vampire_newblood_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_von_carstein_knight" default_group="cavalry" level="26" name="{=str_tor_vc_von_carstein_knight}Von Carstein Knight" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_drakenhof_templar" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_von_carstein_template" />
      <EquipmentSet id="tor_vc_von_carstein_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_drakenhof_templar" default_group="cavalry" level="31" name="{=str_tor_vc_drakenhof_templar}Drakenhof Templar" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_drakenhof_kastelan" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_drakenhof_templar_template" />
      <EquipmentSet id="tor_vc_drakenhof_templar_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_drakenhof_kastelan" default_group="cavalry" level="41" name="{=str_tor_vc_drakenhof_kastelan}Drakenhof Kastelan" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level41" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_drakenhof_kastelan_template" />
      <EquipmentSet id="tor_vc_drakenhof_kastelan_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bd_blooddragon_initiate" default_group="infantry" level="21" name="{=str_tor_bd_blooddragon_initiate}Blood Dragon Initiate" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_bd_blooddragon_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_vampire_newblood_template" />
      <EquipmentSet id="tor_vc_vampire_newblood_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bd_blooddragon_knight" default_group="cavalry" level="26" name="{=str_tor_bd_blooddragon_knight}Blood Dragon Knight" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_bd_blooddragon_templar" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_blood_knight_template" />
      <EquipmentSet id="tor_vc_blood_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bd_blooddragon_templar" default_group="cavalry" level="31" name="{=str_tor_bd_blooddragon_templar}Blood Dragon Templar" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_bd_blooddragon_kastelan" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_blooddragon_templar_template" />
      <EquipmentSet id="tor_blooddragon_templar_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bd_blooddragon_kastelan" default_group="cavalry" level="41" name="{=str_tor_bd_blooddragon_kastelan}Blood Dragon Kastelan" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level41" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_blooddragon_templar_template" />
      <EquipmentSet id="tor_blooddragon_templar_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_lahmian_sister" default_group="horsearcher" level="21" name="{=str_tor_vc_lahmian_sister}Lahmian Sister" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" is_female="true" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire_female" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_lahmian_noblewoman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_von_carstein_sister_template" />
      <EquipmentSet id="tor_vc_von_carstein_sister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_lahmian_noblewoman" default_group="cavalry" level="26" name="{=str_tor_vc_lahmian_noblewoman}Lahmian Noblewoman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26" is_female="true" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire_female" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_lahmian_countess" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_von_carstein_noblewoman_template" />
      <EquipmentSet id="tor_vc_von_carstein_noblewoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_lahmian_countess" default_group="cavalry" level="36" name="{=str_tor_vc_lahmian_countess}Lahmian Countess" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level36" is_female="true" race="vampire">
    <face>
      <face_key_template value="BodyProperty.vampire_female" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_lahmian_countess_template" />
      <EquipmentSet id="tor_vc_lahmian_countess_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_caravan_captain" default_group="cavalry" level="26" name="{=str_tor_vc_caravan_captain}Sylvanian Caravan Captain" occupation="CaravanGuard" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_captain_template" />
      <EquipmentSet id="tor_empire_caravan_captain_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_caravan_guard" default_group="cavalry" level="21" name="{=str_tor_vc_caravan_guard}Caravan Guard Cavalry" occupation="CaravanGuard" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_guard_template" />
      <EquipmentSet id="tor_empire_caravan_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_vc_caravan_ranged}Sylvanian Crossbowman" occupation="CaravanGuard" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_caravan_ranged_template" />
      <EquipmentSet id="tor_empire_caravan_ranged_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_enslaved_villager" default_group="infantry" level="1" name="{=str_tor_chaos_enslaved_villager}Thrall" occupation="Villager" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level1" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_tribesman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_villager_template" />
      <EquipmentSet id="tor_empire_villager_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_tribesman" default_group="infantry" level="6" name="{=str_tor_chaos_norscan_tribesman}Norscan Tribesman" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_raider" />
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_whaler" />
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_horseman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_tribesman_template" />
      <EquipmentSet id="tor_chaos_norscan_tribesman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_raider" default_group="infantry" level="11" name="{=str_tor_chaos_norscan_raider}Norscan Raider" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_marauder" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_raider_template" />
      <EquipmentSet id="tor_chaos_norscan_raider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_marauder" default_group="infantry" level="16" name="{=str_tor_chaos_norscan_marauder}Norscan Marauder" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_champion" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_marauder_template" />
      <EquipmentSet id="tor_chaos_norscan_marauder_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_champion" default_group="infantry" level="26" name="{=str_tor_chaos_norscan_champion}Norscan Marauder Champion" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level26" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_champion_template" />
      <EquipmentSet id="tor_chaos_norscan_champion_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_whaler" default_group="ranged" level="11" name="{=str_tor_chaos_norscan_whaler}Norscan Whaler" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_reaver" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_whaler_template" />
      <EquipmentSet id="tor_chaos_norscan_whaler_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_reaver" default_group="ranged" level="16" name="{=str_tor_chaos_norscan_reaver}Norscan Reaver" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_reaver_template" />
      <EquipmentSet id="tor_chaos_norscan_reaver_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_horseman" default_group="horsearcher" level="11" name="{=str_tor_chaos_norscan_horseman}Norscan Horseman" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_norscan_horsemaster" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_horseman_template" />
      <EquipmentSet id="tor_chaos_norscan_horseman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_norscan_horsemaster" default_group="horsearcher" level="16" name="{=str_tor_chaos_norscan_horsemaster}Norscan Horsemaster" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_horsemaster_template" />
      <EquipmentSet id="tor_chaos_norscan_horsemaster_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_aspiring_warrior" default_group="infantry" level="26" name="{=str_tor_chaos_aspiring_warrior}Aspiring Warrior" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level26" is_basic_troop="true" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_pit_fighter" />
      <upgrade_target id="NPCCharacter.tor_chaos_pugulist" />
      <upgrade_target id="NPCCharacter.tor_chaos_gladiator" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_aspiring_warrior_template" />
      <EquipmentSet id="tor_chaos_aspiring_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_pit_fighter" default_group="infantry" level="31" name="{=str_tor_chaos_pit_fighter}Chaos Pit Fighter" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level31" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_nurgle_warrior" />
      <upgrade_target id="NPCCharacter.tor_chaos_tzeench_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_pit_fighter_template" />
      <EquipmentSet id="tor_chaos_pit_fighter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_pugulist" default_group="infantry" level="31" name="{=str_tor_chaos_pugulist}Chaos Pugulist" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level31" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_undivided_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_pugulist_template" />
      <EquipmentSet id="tor_chaos_pugulist_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_undivided_warrior" default_group="infantry" level="36" name="{=str_tor_chaos_undivided_warrior}Undivided Chaos Warrior" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level36" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_undivided_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_undivided_warrior_template" />
      <EquipmentSet id="tor_chaos_undivided_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_gladiator" default_group="infantry" level="31" name="{=str_tor_chaos_gladiator}Chaos Gladiator" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level31" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_slaanesh_warrior" />
      <upgrade_target id="NPCCharacter.tor_chaos_khorne_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_gladiator_template" />
      <EquipmentSet id="tor_chaos_gladiator_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_slaanesh_warrior" default_group="infantry" level="36" name="{=str_tor_chaos_slaanesh_warrior}Chaos Warrior of Slaanesh" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level36" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_slaanesh_warrior_template" />
      <EquipmentSet id="tor_chaos_slaanesh_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_khorne_warrior" default_group="infantry" level="36" name="{=str_tor_chaos_khorne_warrior}Chaos Warrior of Khorne" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level36" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_khorne_warrior_template" />
      <EquipmentSet id="tor_chaos_khorne_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_nurgle_warrior" default_group="infantry" level="36" name="{=str_tor_chaos_nurgle_warrior}Chaos Warrior of Nurgle" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level36" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_nurgle_warrior_template" />
      <EquipmentSet id="tor_chaos_nurgle_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_tzeench_warrior" default_group="infantry" level="36" name="{=str_tor_chaos_tzeench_warrior}Chaos Warrior of Tzeentch" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level36" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_tzeench_warrior_template" />
      <EquipmentSet id="tor_chaos_tzeench_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_undivided_knight" default_group="cavalry" level="46" name="{=str_tor_chaos_undivided_knight}Undivided Chaos Knight" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level46" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_undivided_knight_template" />
      <EquipmentSet id="tor_chaos_undivided_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_caravan_ranged" default_group="ranged" level="21" name="{=str_tor_chaos_caravan_ranged}Enthralled Caravan Bowman" occupation="CaravanGuard" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level21" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_peasant_archer_template" />
      <EquipmentSet id="tor_br_peasant_archer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_caravan_guard" default_group="ranged" level="21" name="{=str_tor_chaos_caravan_guard}Enthralled Caravan Guard" occupation="CaravanGuard" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level21" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_caravan_horseman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_hunter_template" />
      <EquipmentSet id="tor_br_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_caravan_horseman" default_group="cavalry" level="26" name="{=str_tor_chaos_caravan_horseman}Enthralled Caravan Horseman" occupation="CaravanGuard" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level26" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_mounted_yeoman_template" />
      <EquipmentSet id="tor_br_mounted_yeoman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_caravan_captain" default_group="infantry" level="26" name="{=str_tor_chaos_caravan_captain}Enthralled Caravan Master" occupation="CaravanGuard" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level26" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_manatarms_template" />
      <EquipmentSet id="tor_br_manatarms_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_cultist_of_chaos" default_group="infantry" level="6" name="{=str_tor_chaos_cultist_of_chaos}Cultist Militia of Chaos" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_darksoul_of_chaos" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_cult_acolyte_template" />
      <EquipmentSet id="tor_chaos_cult_acolyte_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_darksoul_of_chaos" default_group="infantry" level="11" name="{=str_tor_chaos_darksoul_of_chaos}Darksoul Militia of Chaos" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_acolyte_of_chaos" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_cult_darksoul_template" />
      <EquipmentSet id="tor_chaos_cult_darksoul_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_acolyte_of_chaos" default_group="infantry" level="16" name="{=str_tor_chaos_acolyte_of_chaos}Acolyte of Chaos" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_cult_magister_template" />
      <EquipmentSet id="tor_chaos_cult_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_ungor" default_group="infantry" level="11" name="{=str_tor_beast_ungor}Ungor" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level11" race="ungor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_beast_shielded_ungor" />
      <upgrade_target id="NPCCharacter.tor_beast_ungor_raider" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_beast_ungor_template" />
      <EquipmentSet id="tor_beast_ungor_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_shielded_ungor" default_group="infantry" level="16" name="{=str_tor_beast_shielded_ungor}Shielded Ungor" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level16" race="ungor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_beast_ungor_chief" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_beast_shielded_ungor_template" />
      <EquipmentSet id="tor_beast_shielded_ungor_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_ungor_raider" default_group="ranged" level="16" name="{=str_tor_beast_ungor_raider}Ungor Raider" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level16" race="ungor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_beast_ungor_raider_template" />
      <EquipmentSet id="tor_beast_ungor_raider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_ungor_chief" default_group="infantry" level="21" name="{=str_tor_beast_ungor_chief}Ungor Chief" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level21" race="ungor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_beast_ungor_chief_template" />
      <EquipmentSet id="tor_beast_ungor_chief_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_gor" default_group="infantry" level="26" name="{=str_tor_beast_gor}Gor" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level26" is_basic_troop="true" race="gor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_beast_gor_foerender" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_beast_gor_template" />
      <EquipmentSet id="tor_beast_gor_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_gor_foerender" default_group="infantry" level="31" name="{=str_tor_beast_gor_foerender}Gor Foe render" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level31" race="gor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_beast_gor_manripper" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_beast_gor_foerender_template" />
      <EquipmentSet id="tor_beast_gor_foerender_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_gor_manripper" default_group="infantry" level="36" name="{=str_tor_beast_gor_manripper}Gor Manripper" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level36" race="gor">
    <face>
      <face_key_template value="BodyProperty.ungor" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_beast_gor_manripper_template" />
      <EquipmentSet id="tor_beast_gor_manripper_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_beast_minotaur" default_group="infantry" level="46" name="{=str_tor_beast_minotaur}Minotaur" occupation="Bandit" culture="Culture.steppe_bandits" skill_template="SkillSet.tor_skills_level46" is_basic_troop="true" race="medium_humanoid_monster">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_beast_minotaur_template" />
      <EquipmentSet id="tor_beast_minotaur_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_braganza_01" default_group="ranged" level="21" name="{=str_tor_dog_braganza_01}Besieger Crossbowman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_braganza_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_besieger_crossbowman_template" />
      <EquipmentSet id="tor_dog_besieger_crossbowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_braganza_02" default_group="ranged" level="26" name="{=str_tor_dog_braganza_02}Besieger Arbalest" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_braganza_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_besieger_arbalest_template" />
      <EquipmentSet id="tor_dog_besieger_arbalest_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_braganza_03" default_group="ranged" level="31" name="{=str_tor_dog_braganza_03}Braganza's Besieger" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_braganza_besieger_template" />
      <EquipmentSet id="tor_dog_braganza_besieger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_cursed_company_01" default_group="infantry" level="21" name="{=str_tor_dog_cursed_company_01}Enslaved Warrior" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_cursed_company_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_the_enslaved_template" />
      <EquipmentSet id="tor_dog_the_enslaved_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_cursed_company_02" default_group="infantry" level="26" name="{=str_tor_dog_cursed_company_02}Damned Warrior" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_cursed_company_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_the_damned_warrior_template" />
      <EquipmentSet id="tor_dog_the_damned_warrior_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_cursed_company_03" default_group="infantry" level="31" name="{=str_tor_dog_cursed_company_03}Cursed Companyman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_the_cursed_template" />
      <EquipmentSet id="tor_dog_the_cursed_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_leopard_01" default_group="infantry" level="21" name="{=str_tor_dog_leopard_01}Leopard Mercenary" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_leopard_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_leopard_mercenary_template" />
      <EquipmentSet id="tor_dog_leopard_mercenary_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_leopard_02" default_group="infantry" level="26" name="{=str_tor_dog_leopard_02}Leopard Pikeman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_leopard_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_leopard_pikeman_template" />
      <EquipmentSet id="tor_dog_leopard_pikeman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_leopard_03" default_group="infantry" level="31" name="{=str_tor_dog_leopard_03}Leopold's Leopard Pikeman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_leopold_leopard_pikeman_template" />
      <EquipmentSet id="tor_dog_leopold_leopard_pikeman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_voland_01" default_group="cavalry" level="21" name="{=str_tor_dog_voland_01}Venator Mercenary Horseman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_voland_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_venator_horseman_template" />
      <EquipmentSet id="tor_dog_venator_horseman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_voland_02" default_group="cavalry" level="26" name="{=str_tor_dog_voland_02}Venator Condottiero" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_voland_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_voland_condotierro_template" />
      <EquipmentSet id="tor_dog_voland_condotierro_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_voland_03" default_group="cavalry" level="31" name="{=str_tor_dog_voland_03}Voland's Venator" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_voland_venator_template" />
      <EquipmentSet id="tor_dog_voland_venator_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_muktar_01" default_group="cavalry" level="21" name="{=str_tor_dog_muktar_01}Arabyian Mercenary Horseman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_muktar_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_arabyian_mercenary_horseman_template" />
      <EquipmentSet id="tor_dog_arabyian_mercenary_horseman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_muktar_02" default_group="cavalry" level="26" name="{=str_tor_dog_muktar_02}Arabyian Desert Destrier" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_muktar_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_arabyian_desert_destrier_template" />
      <EquipmentSet id="tor_dog_arabyian_desert_destrier_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_muktar_03" default_group="cavalry" level="31" name="{=str_tor_dog_muktar_03}Al Muktar's Desert Dog" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_aserai" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_muktar_desert_dog_template" />
      <EquipmentSet id="tor_dog_muktar_desert_dog_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_miragliano_01" default_group="ranged" level="21" name="{=str_tor_dog_miragliano_01}Crossbowman of Miragliano" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_miragliano_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_crossbowman_miragliano_template" />
      <EquipmentSet id="tor_dog_crossbowman_miragliano_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_miragliano_02" default_group="ranged" level="26" name="{=str_tor_dog_miragliano_02}Arbelast of Miragliano" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_miragliano_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_arbelast_miragliano_template" />
      <EquipmentSet id="tor_dog_arbelast_miragliano_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_miragliano_03" default_group="ranged" level="31" name="{=str_tor_dog_miragliano_03}Marksman of Miragliano" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_marksman_miragliano_template" />
      <EquipmentSet id="tor_dog_marksman_miragliano_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_ricco_01" default_group="infantry" level="21" name="{=str_tor_dog_ricco_01}Reman Patriot" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_ricco_02" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_reman_patriot_template" />
      <EquipmentSet id="tor_dog_reman_patriot_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_ricco_02" default_group="infantry" level="26" name="{=str_tor_dog_ricco_02}Reman Pikeman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_dog_ricco_03" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_dog_reman_pikeman_template" />
      <EquipmentSet id="tor_dog_reman_pikeman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_dog_ricco_03" default_group="infantry" level="31" name="{=str_tor_dog_ricco_03}Ricco's Republican Guard" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_dog_riccos_republican_guard_template" />
      <EquipmentSet id="tor_dog_riccos_republican_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_merc_free_company_volunteer" default_group="infantry" level="6" name="{=str_tor_merc_free_company_volunteer}Free Company Volunteer" occupation="Mercenary" culture="Culture.empire" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_merc_free_company_scout" />
      <upgrade_target id="NPCCharacter.tor_merc_free_company_militiaman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_merc_free_company_volunteer_template" />
      <EquipmentSet id="tor_merc_free_company_volunteer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_merc_free_company_scout" default_group="ranged" level="11" name="{=str_tor_merc_free_company_scout}Free Company Scout" occupation="Mercenary" culture="Culture.empire" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_merc_free_company_bowman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_merc_free_company_scout_template" />
      <EquipmentSet id="tor_merc_free_company_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_merc_free_company_militiaman" default_group="infantry" level="11" name="{=str_tor_merc_free_company_militiaman}Free Company Militiaman" occupation="Mercenary" culture="Culture.empire" skill_template="SkillSet.tor_skills_level11">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_merc_free_company_pistolier" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_merc_free_company_miltiaman_template" />
      <EquipmentSet id="tor_merc_free_company_miltiaman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_merc_free_company_bowman" default_group="ranged" level="16" name="{=str_tor_merc_free_company_bowman}Free Company Bowman" occupation="Mercenary" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_merc_free_company_bowman_template" />
      <EquipmentSet id="tor_merc_free_company_bowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_merc_free_company_pistolier" default_group="ranged" level="16" name="{=str_tor_merc_free_company_pistolier}Free Company Pistolier" occupation="Mercenary" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_merc_free_company_pistolier_template" />
      <EquipmentSet id="tor_merc_free_company_pistolier_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_kragsburg_retainer" default_group="infantry" level="16" name="{=str_tor_ror_kragsburg_retainer}Von Kragsburg Retainer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_kragsburg_sergeant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_kragsburg_retainer_template" />
      <EquipmentSet id="tor_kragsburg_retainer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_kragsburg_sergeant" default_group="infantry" level="21" name="{=str_tor_ror_kragsburg_sergeant}Von Kragsburg Sergeant" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_kragsburg_house_guard" />
      <upgrade_target id="NPCCharacter.tor_ror_kragsburg_personal_guard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_kragsburg_sergeant_template" />
      <EquipmentSet id="tor_kragsburg_sergeant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_kragsburg_house_guard" default_group="infantry" level="26" name="{=str_tor_ror_kragsburg_house_guard}Von Kragsburg House Guard" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_kragsburg_house_guard_template" />
      <EquipmentSet id="tor_kragsburg_house_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_kragsburg_personal_guard" default_group="infantry" level="26" name="{=str_tor_ror_kragsburg_personal_guard}Von Kragsburg Personal Guard" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_kragsburg_personal_guard_template" />
      <EquipmentSet id="tor_kragsburg_personal_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_stir_river_scout" default_group="ranged" level="16" name="{=str_tor_ror_stir_river_scout}Stir River Scout" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_stir_river_patrolman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_stir_river_scout_template" />
      <EquipmentSet id="tor_stir_river_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_stir_river_patrolman" default_group="ranged" level="21" name="{=str_tor_ror_stir_river_patrolman}Stir River Patrolman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_stir_river_patrol_guardsman" />
      <upgrade_target id="NPCCharacter.tor_ror_stir_river_patrol_nimrod" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_stir_river_patrolman_template" />
      <EquipmentSet id="tor_stir_river_patrolman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_stir_river_patrol_guardsman" default_group="infantry" level="26" name="{=str_tor_ror_stir_river_patrol_guardsman}Stir River Patrol Guardsman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_stir_river_patrol_guardsman_template" />
      <EquipmentSet id="tor_stir_river_patrol_guardsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_stir_river_patrol_nimrod" default_group="ranged" level="26" name="{=str_tor_ror_stir_river_patrol_nimrod}Stir River Patrol Nimrod" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_stir_river_patrol_nimrod_template" />
      <EquipmentSet id="tor_stir_river_patrol_nimrod_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_blazing_sun_initiate" default_group="cavalry" level="21" name="{=str_tor_ror_blazing_sun_initiate}Blazing Sun Initiate" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_blazing_sun_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_blazing_sun_initiate_template" />
      <EquipmentSet id="tor_blazing_sun_initiate_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_blazing_sun_knight" default_group="cavalry" level="26" name="{=str_tor_ror_blazing_sun_knight}Knight of the Blazing Sun" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_blazing_sun_preceptor" />
      <upgrade_target id="NPCCharacter.tor_ror_blazing_sun_demigryph_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_blazing_sun_knight_template" />
      <EquipmentSet id="tor_blazing_sun_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_blazing_sun_preceptor" default_group="cavalry" level="36" name="{=str_tor_ror_blazing_sun_preceptor}Preceptor of the Blazing Sun" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_blazing_sun_innercircle" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_blazing_sun_preceptor_template" />
      <EquipmentSet id="tor_blazing_sun_preceptor_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_blazing_sun_demigryph_knight" default_group="cavalry" level="36" name="{=str_tor_ror_blazing_sun_demigryph_knight}Demigryph Knight of the Blazing Sun" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_blazing_sun_demigryph_innercircle" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_blazing_sun_demigryph_knight_template" />
      <EquipmentSet id="tor_blazing_sun_demigryph_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_blazing_sun_innercircle" default_group="cavalry" level="46" name="{=str_tor_ror_blazing_sun_innercircle}Inner Circle Knight of the Blazing Sun" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_blazing_sun_innercircle_template" />
      <EquipmentSet id="tor_blazing_sun_innercircle_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_blazing_sun_demigryph_innercircle" default_group="cavalry" level="46" name="{=str_tor_ror_blazing_sun_demigryph_innercircle}Inner Circle Demigryph of the Blazing Sun" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_blazing_sun_demigryph_innercircle_template" />
      <EquipmentSet id="tor_blazing_sun_demigryph_innercircle_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_grundel_defender_troop" default_group="infantry" level="16" name="{=str_tor_ror_grundel_defender_troop}Grundel's Defender Troop" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_grundel_defender_stalwart" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_grundel_defender_troop_template" />
      <EquipmentSet id="tor_grundel_defender_troop_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_grundel_defender_stalwart" default_group="infantry" level="21" name="{=str_tor_ror_grundel_defender_stalwart}Stalwart Grundel's Defender" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_grundel_defender_defender" />
      <upgrade_target id="NPCCharacter.tor_ror_grundel_defender_sergeant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_grundel_defender_stalwart_template" />
      <EquipmentSet id="tor_grundel_defender_stalwart_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_grundel_defender_sergeant" default_group="infantry" level="26" name="{=str_tor_ror_grundel_defender_sergeant}Grundel's Defender Sergeant" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_grundel_defender_sergeant_template" />
      <EquipmentSet id="tor_grundel_defender_sergeant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_grundel_defender_defender" default_group="infantry" level="26" name="{=str_tor_ror_grundel_defender_defender}Brave Grundel's Defender" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_grundel_defender_defender_template" />
      <EquipmentSet id="tor_grundel_defender_defender_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_deathjack_longbowman" default_group="ranged" level="16" name="{=str_tor_ror_deathjack_longbowman}Deathjack Longbowman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_deathjack_marksman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_deathjack_longbowman_template" />
      <EquipmentSet id="tor_deathjack_longbowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_deathjack_marksman" default_group="ranged" level="21" name="{=str_tor_ror_deathjack_marksman}Deathjack Marksman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_deathjack_toxophilite" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_deathjack_marksman_template" />
      <EquipmentSet id="tor_deathjack_marksman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_deathjack_toxophilite" default_group="ranged" level="26" name="{=str_tor_ror_deathjack_toxophilite}Deathjack Toxophilite" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_deathjack_toxophilite_template" />
      <EquipmentSet id="tor_deathjack_toxophilite_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_ward_of_taal" default_group="infantry" level="16" name="{=str_tor_ror_ward_of_taal}Ward of Taal" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.hornedhunter" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_hunter_of_taal" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_wardoftaal_template" />
      <EquipmentSet id="tor_wardoftaal_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_hunter_of_taal" default_group="infantry" level="21" name="{=str_tor_ror_hunter_of_taal}Hunter of Taal" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.hornedhunter" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_horned_hunter" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_hunteroftaal_template" />
      <EquipmentSet id="tor_hunteroftaal_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_horned_hunter" default_group="infantry" level="26" name="{=str_tor_ror_horned_hunter}Horned Hunter" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.hornedhunter" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_hornedhunter_template" />
      <EquipmentSet id="tor_hornedhunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_longshank_initiate" default_group="ranged" level="11" name="{=str_tor_ror_longshank_initiate}Longshank Initiate" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_longshank_strider" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_longshank_initiate_template" />
      <EquipmentSet id="tor_longshank_initiate_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_longshank_strider" default_group="ranged" level="16" name="{=str_tor_ror_longshank_strider}Longshank Strider" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_longshank_warden" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_longshank_strider_template" />
      <EquipmentSet id="tor_longshank_strider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_longshank_warden" default_group="ranged" level="21" name="{=str_tor_ror_longshank_warden}Longshank Warden" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_longshank_ranger" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_longshank_warden_template" />
      <EquipmentSet id="tor_longshank_warden_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_longshank_ranger" default_group="ranged" level="26" name="{=str_tor_ror_longshank_ranger}Longshank Ranger" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_longshank_ranger_template" />
      <EquipmentSet id="tor_longshank_ranger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_griffon_initiate" default_group="infantry" level="31" name="{=str_tor_ror_griffon_initiate}Griffon Initiate" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_griffon_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_griffon_initiate_template" />
      <EquipmentSet id="tor_ror_griffon_initiate_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_griffon_knight" default_group="cavalry" level="36" name="{=str_tor_ror_griffon_knight}Griffon Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_griffon_preceptor_knight" />
      <upgrade_target id="NPCCharacter.tor_ror_griffon_demi_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_griffon_knight_template" />
      <EquipmentSet id="tor_ror_griffon_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_griffon_preceptor_knight" default_group="cavalry" level="41" name="{=str_tor_ror_griffon_preceptor_knight}Griffon Preceptor Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_griffon_innercircle_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_griffon_preceptor_knight_template" />
      <EquipmentSet id="tor_ror_griffon_preceptor_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_griffon_innercircle_knight" default_group="cavalry" level="46" name="{=str_tor_ror_griffon_innercircle_knight}Inner Circle Griffon Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ror_griffon_innercircle_knight_template" />
      <EquipmentSet id="tor_ror_griffon_innercircle_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_griffon_demi_knight" default_group="cavalry" level="41" name="{=str_tor_ror_griffon_demi_knight}Demigryph Griffon Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_griffon_demi_preceptor_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_griffon_demi_knight_template" />
      <EquipmentSet id="tor_ror_griffon_demi_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_griffon_demi_preceptor_knight" default_group="cavalry" level="46" name="{=str_tor_ror_griffon_demi_preceptor_knight}Demigryph Preceptor Griffon Knight" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ror_griffon_demi_preceptor_knight_template" />
      <EquipmentSet id="tor_ror_griffon_demi_preceptor_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_son_of_altdorf" default_group="infantry" level="21" name="{=str_tor_ror_son_of_altdorf}Son of Altdorf" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_altdorf_companyman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_son_of_altdorf_template" />
      <EquipmentSet id="tor_ror_son_of_altdorf_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_altdorf_companyman" default_group="infantry" level="26" name="{=str_tor_ror_altdorf_companyman}Altdorf Companyman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_altdorf_company_sergeant" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_altdorf_companyman_template" />
      <EquipmentSet id="tor_ror_altdorf_companyman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_altdorf_company_sergeant" default_group="infantry" level="31" name="{=str_tor_ror_altdorf_company_sergeant}Altdorf Company Sergeant" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ror_altdorf_company_sergeant_template" />
      <EquipmentSet id="tor_ror_altdorf_company_sergeant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_hochland_scout" default_group="ranged" level="21" name="{=str_tor_ror_hochland_scout}Hochland Scout" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_hochland_longrifle" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_hochland_scout_template" />
      <EquipmentSet id="tor_hochland_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_hochland_longrifle" default_group="ranged" level="26" name="{=str_tor_ror_hochland_longrifle}Hochland Long Rifle" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_hergig_jaegerkorps" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_hochland_longrifle_template" />
      <EquipmentSet id="tor_hochland_longrifle_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_hergig_jaegerkorps" default_group="ranged" level="36" name="{=str_tor_ror_hergig_jaegerkorps}Hergig Jaegerkorps Marksman" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_hergig_jaegerkorps_template" />
      <EquipmentSet id="tor_hergig_jaegerkorps_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_ostland_shield" default_group="infantry" level="16" name="{=str_tor_ror_ostland_shield}Shield of Ostland" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_ostland_blackguard" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_shield_ostland_template" />
      <EquipmentSet id="tor_shield_ostland_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_ostland_blackguard" default_group="infantry" level="41" name="{=str_tor_ror_ostland_blackguard}Ostland Black Guard" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level41">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ostland_blackguard_template" />
      <EquipmentSet id="tor_ostland_blackguard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_hochland_drakwald_patrol" default_group="ranged" level="21" name="{=str_tor_ror_hochland_drakwald_patrol}Hochland Drakwald Patrol" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_gundermans_surefire" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_hochland_drakwald_patrol_template" />
      <EquipmentSet id="tor_hochland_drakwald_patrol_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_gundermans_surefire" default_group="ranged" level="26" name="{=str_tor_ror_gundermans_surefire}Gunderman's Surefire" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_gundermans_surefire_template" />
      <EquipmentSet id="tor_gundermans_surefire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_marienburg_sailor" default_group="infantry" level="6" name="{=str_tor_ror_marienburg_sailor}Marienburg Sailor" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_marienburg_private_militia" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_marienburg_sailor_template" />
      <EquipmentSet id="tor_marienburg_sailor_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_marienburg_private_militia" default_group="ranged" level="16" name="{=str_tor_ror_marienburg_private_militia}Marienburg Private Militia" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_klumpfs_buccaneer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_marienburg_private_militia_template" />
      <EquipmentSet id="tor_marienburg_private_militia_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_klumpfs_buccaneer" default_group="ranged" level="26" name="{=str_tor_ror_klumpfs_buccaneer}Van Klumpf Buccaneer" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_klumpf_buccaneers_template" />
      <EquipmentSet id="tor_klumpf_buccaneers_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_nordland_mariner" default_group="infantry" level="16" name="{=str_tor_ror_nordland_mariner}Nordland Marine" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_nordland_seahawk" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_nordland_mariner_template" />
      <EquipmentSet id="tor_nordland_mariner_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_nordland_seahawk" default_group="infantry" level="26" name="{=str_tor_ror_nordland_seahawk}Nordland Seahawk" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_nordland_seahawk_template" />
      <EquipmentSet id="tor_nordland_seahawk_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_sternieste_skeleton" default_group="infantry" level="16" name="{=str_tor_ror_sternieste_skeleton}Castle Sternieste Skeleton" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_sternieste_grave_guard" />
      <upgrade_target id="NPCCharacter.tor_ror_sternieste_black_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_sternieste_skeleton_template" />
      <EquipmentSet id="tor_sternieste_skeleton_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_sternieste_grave_guard" default_group="infantry" level="26" name="{=str_tor_ror_sternieste_grave_guard}Castle Sternieste Grave Guard" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_sternieste_sternsmen" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_sternieste_grave_guard_template" />
      <EquipmentSet id="tor_sternieste_grave_guard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_sternieste_sternsmen" default_group="infantry" level="31" name="{=str_tor_ror_sternieste_sternsmen}Sternsman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_sternieste_sternsman_template" />
      <EquipmentSet id="tor_sternieste_sternsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_sternieste_black_knight" default_group="cavalry" level="26" name="{=str_tor_ror_sternieste_black_knight}Castle Sternieste Black Knight" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level26" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_sternieste_reaver" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_sternieste_black_knight_template" />
      <EquipmentSet id="tor_sternieste_black_knight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_sternieste_reaver" default_group="cavalry" level="31" name="{=str_tor_ror_sternieste_reaver}Verek's Reaver" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_sternieste_vereks_reaver_template" />
      <EquipmentSet id="tor_sternieste_vereks_reaver_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_konigstein_skeleton" default_group="infantry" level="11" name="{=str_tor_ror_konigstein_skeleton}Konigstein Stalker" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level11" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_konigstein_swordsman" />
      <upgrade_target id="NPCCharacter.tor_ror_konigstein_spearman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_konigstein_skeleton_template" />
      <EquipmentSet id="tor_konigstein_skeleton_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_konigstein_swordsman" default_group="infantry" level="16" name="{=str_tor_ror_konigstein_swordsman}Konigstein Stalker Swordsman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_konigstein_sword_template" />
      <EquipmentSet id="tor_konigstein_sword_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_konigstein_spearman" default_group="infantry" level="16" name="{=str_tor_ror_konigstein_spearman}Konigstein Stalker Spearman" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level16" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_konigstein_spear_template" />
      <EquipmentSet id="tor_konigstein_spear_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_peasant_squire" default_group="infantry" level="16" name="{=str_tor_ror_peasant_squire}Wainfleet Peasant Squires" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_peasant_squight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_peasant_squire_template" />
      <EquipmentSet id="tor_ror_peasant_squire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_peasant_squight" default_group="infantry" level="36" name="{=str_tor_ror_peasant_squight}Wainfleet Peasant Squight" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_peasant_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_peasant_squight_template" />
      <EquipmentSet id="tor_ror_peasant_squight_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_peasant_knight" default_group="infantry" level="46" name="{=str_tor_ror_peasant_knight}Wainfleet Peasant Knight" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ror_peasant_knight" />
      <EquipmentSet id="tor_ror_peasant_knight" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_maisontaal_cultist" default_group="ranged" level="16" name="{=str_tor_ror_maisontaal_cultist}La Maisontaal Cultist" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_maisontaal_acetic" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_maisontaal_cultist_template" />
      <EquipmentSet id="tor_ror_maisontaal_cultist_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_maisontaal_acetic" default_group="ranged" level="36" name="{=str_tor_ror_maisontaal_acetic}La Maisontaal Acetic" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_maisontaal_warden" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_maisontaal_acetic_template" />
      <EquipmentSet id="tor_ror_maisontaal_acetic_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_maisontaal_warden" default_group="ranged" level="46" name="{=str_tor_ror_maisontaal_warden}Holy Warden of La Maisontaal" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ror_maisontaal_warden_template" />
      <EquipmentSet id="tor_ror_maisontaal_warden_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_bergerac_herrimault" default_group="ranged" level="16" name="{=str_tor_ror_bergerac_herrimault}Honourable Herrimault" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_bergerac_ranger" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_bergerac_herrimault_template" />
      <EquipmentSet id="tor_ror_bergerac_herrimault_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_bergerac_ranger" default_group="ranged" level="36" name="{=str_tor_ror_bergerac_ranger}Bergerac Ranger" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_bergerac_bowman" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_ror_bergerac_ranger_template" />
      <EquipmentSet id="tor_ror_bergerac_ranger_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_bergerac_bowman" default_group="ranged" level="46" name="{=str_tor_ror_bergerac_bowman}Bowman of Bergerac" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ror_bergerac_bowman_template" />
      <EquipmentSet id="tor_ror_bergerac_bowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_anguille_sailor" default_group="infantry" level="11" name="{=str_tor_ror_anguille_sailor}L'Anguille Sailor" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_corduin_mariner" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_anguille_sailor_template" />
      <EquipmentSet id="tor_anguille_sailor_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_corduin_mariner" default_group="infantry" level="21" name="{=str_tor_ror_corduin_mariner}Corduin's Mariner" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_corduin_knightly_mariner" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_corduins_mariners_template" />
      <EquipmentSet id="tor_corduins_mariners_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_corduin_knightly_mariner" default_group="infantry" level="26" name="{=str_tor_ror_corduin_knightly_mariner}Corduin's Knightly Mariner" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_corduins_knightly_mariners_template" />
      <EquipmentSet id="tor_corduins_knightly_mariners_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_carcasonne_mountain_guard" default_group="infantry" level="16" name="{=str_tor_ror_carcasonne_mountain_guard}Carcasonne Mountain Guard" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_kharmourts_blades" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_carcasonne_mountainguard_template" />
      <EquipmentSet id="tor_carcasonne_mountainguard_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_kharmourts_blades" default_group="infantry" level="26" name="{=str_tor_ror_kharmourts_blades}Kharmourt's Blades" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_kharmourts_blade_template" />
      <EquipmentSet id="tor_kharmourts_blade_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_br_mounted_bowman" default_group="horsearcher" level="21" name="{=str_tor_ror_br_mounted_bowman}Brettonnian Mounted Bowman" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_warden_montfort" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_brettonian_mounted_bowman_template" />
      <EquipmentSet id="tor_brettonian_mounted_bowman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_warden_montfort" default_group="horsearcher" level="26" name="{=str_tor_ror_warden_montfort}Warden of Montfort" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_warden_montfort_template" />
      <EquipmentSet id="tor_warden_montfort_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_bastonne_squire" default_group="infantry" level="26" name="{=str_tor_ror_bastonne_squire}Bastonne Squire" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level26" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_bastonne_beastslayer" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_bastonne_squire_template" />
      <EquipmentSet id="tor_bastonne_squire_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_bastonne_beastslayer" default_group="infantry" level="31" name="{=str_tor_ror_bastonne_beastslayer}Beastslayer of Bastonne" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level31">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_bastonne_beastslayer_template" />
      <EquipmentSet id="tor_bastonne_beastslayer_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_lyonesse_knight_errant" default_group="cavalry" level="21" name="{=str_tor_ror_lyonesse_knight_errant}Lyonesse Knight Errant" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level21" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_ror_lionhearted_knight" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_lyonesse_knighterrant_template" />
      <EquipmentSet id="tor_lyonesse_knighterrant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_lionhearted_knight" default_group="cavalry" level="36" name="{=str_tor_ror_lionhearted_knight}Knight of the Lionhearted" occupation="Soldier" culture="Culture.vlandia" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_knight_lionhearted_template" />
      <EquipmentSet id="tor_knight_lionhearted_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ror_loec_trickster" default_group="infantry" level="36" name="{=str_tor_ror_loec_trickster}Loec's Trickster" occupation="Soldier" culture="Culture.battania" skill_template="SkillSet.tor_skills_level36" is_basic_troop="true" is_female="true" race="elf">
    <face>
      <face_key_template value="BodyProperty.female_wood_elf" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_loec_trickster_template" />
      <EquipmentSet id="tor_loec_trickster_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wh_retinue" default_group="ranged" level="16" name="{=str_tor_wh_retinue}Retinue Apprentice" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level16" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_wh_retinue_hunter" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_wh_retinue_template" />
      <EquipmentSet id="tor_empire_wh_retinue_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wh_retinue_hunter" default_group="ranged" level="36" name="{=str_tor_wh_retinue_hunter}Retinue Hunter" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level36">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_wh_retinue_headhunter" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_wh_retinue_hunter_template" />
      <EquipmentSet id="tor_empire_wh_retinue_hunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_wh_retinue_headhunter" default_group="ranged" level="46" name="{=str_tor_wh_retinue_headhunter}Retinue Headhunter" occupation="Soldier" culture="Culture.empire" skill_template="SkillSet.tor_skills_level46">
    <face>
      <face_key_template value="BodyProperty.fighter_vlandia" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_wh_retinue_headhunter_template" />
      <EquipmentSet id="tor_empire_wh_retinue_headhunter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_harbinger_champion" default_group="infantry" level="31" name="{=str_tor_vc_harbinger_champion}Necromancer Champion" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level31" is_basic_troop="true" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_harbinger_champion_two_handed" />
      <upgrade_target id="NPCCharacter.tor_vc_harbinger_champion_plate" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_harbinger_champion_template" />
      <EquipmentSet id="tor_vc_harbinger_champion_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_harbinger_champion_two_handed" default_group="infantry" level="36" name="{=str_tor_vc_harbinger_champion_two_handed}Necromancer Champion Two-Handed" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level36" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_harbinger_champion_two_handed_template" />
      <EquipmentSet id="tor_vc_harbinger_champion_two_handed_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_harbinger_champion_plate" default_group="cavalry" level="41" name="{=str_tor_vc_harbinger_champion_plate}Necromancer Champion Plate" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level41" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_vc_harbinger_champion_plate_two_handed" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_harbinger_champion_plate_template" />
      <EquipmentSet id="tor_vc_harbinger_champion_plate_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_vc_harbinger_champion_plate_two_handed" default_group="cavalry" level="46" name="{=str_tor_vc_harbinger_champion_plate_two_handed}Necromancer Champion Two-Handed-Plate" occupation="Soldier" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level46" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_harbinger_champion_plate_two_handed_template" />
      <EquipmentSet id="tor_vc_harbinger_champion_plate_two_handed_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_cult_acolyte" default_group="infantry" level="11" name="{=str_tor_chaos_cult_acolyte}Cultist of Chaos" occupation="Bandit" culture="Culture.forest_bandits" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_cult_darksoul" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_cult_acolyte_template" />
      <EquipmentSet id="tor_chaos_cult_acolyte_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_cult_darksoul" default_group="infantry" level="16" name="{=str_tor_chaos_cult_darksoul}Darksoul of Chaos" occupation="Bandit" culture="Culture.forest_bandits" skill_template="SkillSet.tor_skills_level16" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_chaos_cult_magister" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_cult_darksoul_template" />
      <EquipmentSet id="tor_chaos_cult_darksoul_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_chaos_cult_magister" default_group="infantry" level="21" name="{=str_tor_chaos_cult_magister}Fallen Magister" occupation="Bandit" culture="Culture.forest_bandits" skill_template="SkillSet.tor_skills_level21" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_cult_magister_template" />
      <EquipmentSet id="tor_chaos_cult_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_bw_expelled_magister" default_group="infantry" level="11" name="{=str_tor_cult_bw_expelled_magister}Expelled Magister" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_cult_bw_vengeful_cultist" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_cult_bw_expelled_magister_template" />
      <EquipmentSet id="tor_cult_bw_expelled_magister_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_bw_vengeful_cultist" default_group="infantry" level="16" name="{=str_tor_cult_bw_vengeful_cultist}Vengeful Cultist" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_cult_bw_sorcerer_broken_wheel" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_cult_bw_vengeful_cultist_template" />
      <EquipmentSet id="tor_cult_bw_vengeful_cultist_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_bw_sorcerer_broken_wheel" default_group="infantry" level="21" name="{=str_tor_cult_bw_sorcerer_broken_wheel}Sorcerer of the Broken Wheel" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level21" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_cult_bw_sorcerer_broken_wheel_template" />
      <EquipmentSet id="tor_cult_bw_sorcerer_broken_wheel_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_coi_unlit_candle" default_group="infantry" level="11" name="{=str_tor_cult_coi_unlit_candle}Unlit Candle" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_cult_coi_beacon" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_cult_coi_unlit_candle_template" />
      <EquipmentSet id="tor_cult_coi_unlit_candle_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_coi_beacon" default_group="infantry" level="16" name="{=str_tor_cult_coi_beacon}Beacon" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_cult_coi_order_of_star_acolyte" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_cult_coi_beacon_template" />
      <EquipmentSet id="tor_cult_coi_beacon_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_coi_order_of_star_acolyte" default_group="infantry" level="21" name="{=str_tor_cult_coi_order_of_star_acolyte}Order of the Star Acolyte" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level21" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_cult_coi_order_of_star_acolyte_template" />
      <EquipmentSet id="tor_cult_coi_order_of_star_acolyte_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_fsf_puer_ulceris" default_group="infantry" level="11" name="{=str_tor_cult_fsf_puer_ulceris}Puer Ulceris" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_cult_fsf_ulcus_frater" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_puer_ulceris_template" />
      <EquipmentSet id="tor_cult_fsf_puer_ulceris_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_fsf_ulcus_frater" default_group="infantry" level="16" name="{=str_tor_cult_fsf_ulcus_frater}Ulcus Frater" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level16" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_cult_fsf_ulcerator" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_ulcus_frater_template" />
      <EquipmentSet id="tor_cult_fsf_ulcus_frater_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_cult_fsf_ulcerator" default_group="infantry" level="21" name="{=str_tor_cult_fsf_ulcerator}Ulcerator" occupation="Soldier" culture="Culture.chaos_culture" skill_template="SkillSet.tor_skills_level21" race="chaos_ud_cultist">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_cult_fsf_ulcerator_template" />
      <EquipmentSet id="tor_cult_fsf_ulcerator_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_roaming_undead_skeleton" default_group="infantry" level="6" name="{=str_tor_roaming_undead_skeleton}Roaming Undead" occupation="Bandit" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_roaming_undead_warrior" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_template" />
      <EquipmentSet id="tor_vc_skeleton_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_roaming_undead_warrior" default_group="infantry" level="11" name="{=str_tor_roaming_undead_warrior}Roaming Undead Warrior" occupation="Bandit" culture="Culture.khuzait" skill_template="SkillSet.tor_skills_level11" race="skeleton">
    <face>
      <face_key_template value="BodyProperty.skeleton" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_vc_skeleton_swordsman_template" />
      <EquipmentSet id="tor_vc_skeleton_swordsman_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_outlaw" default_group="infantry" level="6" name="{=str_tor_empire_outlaw}Outlaw" occupation="Bandit" culture="Culture.looters" skill_template="SkillSet.tor_skills_level6" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_outlaw_leader" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_outlaw_template" />
      <EquipmentSet id="tor_empire_outlaw_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_outlaw_leader" default_group="infantry" level="16" name="{=str_tor_empire_outlaw_leader}Outlaw Captain" occupation="Bandit" culture="Culture.looters" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.brute" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_outlaw_leader_template" />
      <EquipmentSet id="tor_empire_outlaw_leader_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter" default_group="infantry" level="11" name="{=str_tor_empire_deserter}Deserter" occupation="Bandit" culture="Culture.mountain_bandits" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_empire_deserter_sergeant" />
      <upgrade_target id="NPCCharacter.tor_empire_deserter_scout" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_template" />
      <EquipmentSet id="tor_empire_deserter_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter_sergeant" default_group="infantry" level="16" name="{=str_tor_empire_deserter_sergeant}Deserter Sergeant" occupation="Bandit" culture="Culture.mountain_bandits" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.brute" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_sergeant_template" />
      <EquipmentSet id="tor_empire_deserter_sergeant_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_empire_deserter_scout" default_group="ranged" level="16" name="{=str_tor_empire_deserter_scout}Deserter Scout" occupation="Bandit" culture="Culture.mountain_bandits" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_empire_deserter_scout_template" />
      <EquipmentSet id="tor_empire_deserter_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_bandit" default_group="ranged" level="11" name="{=str_tor_br_bandit}Hood" occupation="Bandit" culture="Culture.desert_bandits" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_br_bandit_scout" />
      <upgrade_target id="NPCCharacter.tor_br_bandit_leader" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_br_bandit_template" />
      <EquipmentSet id="tor_br_bandit_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_bandit_leader" default_group="ranged" level="16" name="{=str_tor_br_bandit_leader}Herrimault" occupation="Bandit" culture="Culture.desert_bandits" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.brute" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_bandit_leader_template" />
      <EquipmentSet id="tor_br_bandit_leader_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_br_bandit_scout" default_group="infantry" level="16" name="{=str_tor_br_bandit_scout}Thief" occupation="Bandit" culture="Culture.desert_bandits" skill_template="SkillSet.tor_skills_level16">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_br_bandit_scout_template" />
      <EquipmentSet id="tor_br_bandit_scout_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bandit_norscan_raider" default_group="infantry" level="11" name="{=str_tor_bandit_norscan_raider}Renegade Norscan Raider" occupation="Bandit" culture="Culture.sea_raiders" skill_template="SkillSet.tor_skills_level11" is_basic_troop="true" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_bandit_norscan_marauder" />
      <upgrade_target id="NPCCharacter.tor_bandit_norscan_whaler" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_raider_template" />
      <EquipmentSet id="tor_chaos_norscan_raider_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bandit_norscan_whaler" default_group="ranged" level="16" name="{=str_tor_bandit_norscan_whaler}Renegade Norscan Whaler" occupation="Bandit" culture="Culture.sea_raiders" skill_template="SkillSet.tor_skills_level16" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_whaler_template" />
      <EquipmentSet id="tor_chaos_norscan_whaler_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bandit_norscan_marauder" default_group="infantry" level="16" name="{=str_tor_bandit_norscan_marauder}Renegade Norscan Marauder" occupation="Bandit" culture="Culture.sea_raiders" skill_template="SkillSet.tor_skills_level16" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets>
      <upgrade_target id="NPCCharacter.tor_bandit_norscan_champion" />
    </upgrade_targets>
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_marauder_template" />
      <EquipmentSet id="tor_chaos_norscan_marauder_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_bandit_norscan_champion" default_group="infantry" level="26" name="{=str_tor_bandit_norscan_champion}Renegade Norscan Marauder Champion" occupation="Bandit" culture="Culture.sea_raiders" skill_template="SkillSet.tor_skills_level26" race="marauder">
    <face>
      <face_key_template value="BodyProperty.undivided_cultist" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_chaos_norscan_champion_template" />
      <EquipmentSet id="tor_chaos_norscan_champion_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
  <NPCCharacter id="tor_ti_vittorio" default_group="infantry" level="46" name="{=str_tor_ti_vittorio}Vittorio de Luca" occupation="Special" culture="Culture.empire" skill_template="SkillSet.tor_skills_duelist">
    <face>
      <face_key_template value="BodyProperty.fighter_empire" />
    </face>
    <upgrade_targets />
    <Equipments>
      <EquipmentSet id="tor_ti_vittorio_template" />
      <EquipmentSet id="tor_ti_vittorio_template" civilian="true" />
    </Equipments>
  </NPCCharacter>
</NPCCharacters>