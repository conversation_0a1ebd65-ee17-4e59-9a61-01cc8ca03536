<?xml version="1.0" encoding="utf-8"?>
<TriggeredEffectTemplates>
    <!-- Minor magic -->
    <TriggeredEffectTemplate StringID="dart_explosion"
                             BurstParticleEffectPrefab="blue_particle_explosion"
                             SoundEffectId="dart_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="40"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="physical_resistance_20"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="15"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>physical_resistance_20</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_healing_statuseffect"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="9"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>minor_healing_regeneration</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_minor_healing_statuseffect"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="7"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>minor_healing_regeneration</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="foetid_cloud_burst"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="30"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="enchant_weapon"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="20"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.EnchantWeaponScript" />
    <TriggeredEffectTemplate StringID="physical_bonus_20"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="15"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>physical_bonus_20</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="dust_storm_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="5"
                             Radius="3"
                             HasShockWave="true"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <!-- Lore of Fire -->
    <TriggeredEffectTemplate StringID="fireball_explosion"
                             BurstParticleEffectPrefab="psys_fireball_explosion_1"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="70"
                             Radius="6"
                             HasShockWave="false"
                             TargetType="All"
                             ImbuedStatusEffectDuration="5"
                             DamageVariance="0.05"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fireball_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="cinderblast_explosion"
                             BurstParticleEffectPrefab="cinder_explosion"
                             SoundEffectId="none"
                             SoundEffectLength="2.5"
                             DamageType="Physical"
                             DamageAmount="70"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="boltofaqshy_explosion"
                             BurstParticleEffectPrefab="fire_particle_explosion"
                             SoundEffectId="dart_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="65"
                             Radius="2.5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="sear_burst"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Fire"
                             DamageAmount="75"
                             Radius="6"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="apply_holy_sword_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="30"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyHolyItemTraitScript" />
    <TriggeredEffectTemplate StringID="burning_head_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Fire"
                             DamageAmount="40"
                             Radius="3"
                             TargetType="All"
                             ImbuedStatusEffectDuration="5"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fireball_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Lore of Heavens -->
    <TriggeredEffectTemplate StringID="lightningbolt_explosion"
                             BurstParticleEffectPrefab="blue_particle_explosion"
                             SoundEffectId="dart_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Lightning"
                             DamageAmount="65"
                             Radius="2.5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="windblast_burst"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="35"
                             Radius="15"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none"
                             HasShockWave="true" />
    <TriggeredEffectTemplate StringID="phys_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="35"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="lightning_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Lightning"
                             DamageAmount="45"
                             Radius="2"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="physical_resistance_50"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="30"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="50"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>physical_resistance_50</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="blizzard_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="35"
                             Radius="3"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none"
                             HasShockWave="false" />
    <TriggeredEffectTemplate StringID="comet_explosion"
                             BurstParticleEffectPrefab="comet_explosion"
                             DoNotAlignParticleEffectPrefabOnImpact="true"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="225"
                             Radius="15"
                             HasShockWave="true"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="doom_bolt_explosion"
                             BurstParticleEffectPrefab="doom_bolt_explosion"
                             DoNotAlignParticleEffectPrefabOnImpact="true"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="325"
                             Radius="17"
                             HasShockWave="true"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="thunderbolt_explosion"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Lightning"
                             DamageAmount="120"
                             Radius="5"
                             HasShockWave="true"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="curse_of_midnight"
                             BurstParticleEffectPrefab="curse_of_midnight_wind"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             ImbuedStatusEffectDuration="30"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="2">
        <ImbuedStatusEffect>curse_of_midnight_wind_phys</ImbuedStatusEffect>
        <ImbuedStatusEffect>curse_of_midnight_wind_ats</ImbuedStatusEffect>
        <ImbuedStatusEffect>curse_of_midnight_wind_mov</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Lore of Light -->
    <TriggeredEffectTemplate StringID="shemgaze_explosion"
                             BurstParticleEffectPrefab="white_particle_explosion"
                             SoundEffectId="dart_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="55"
                             Radius="2"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="shard_explosion"
                             BurstParticleEffectPrefab="shards_explosion"
                             SoundEffectId="shards_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="140"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="pillar_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="50"
                             Radius="2"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="net_of_amyntok_hex"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="20"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="25"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>net_amyn_movementimpair_100</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_bironas_timewarp"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="20"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="3"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>bironas_timewarp_mov</ImbuedStatusEffect>
        <ImbuedStatusEffect>bironas_timewarp_ats</ImbuedStatusEffect>
        <ImbuedStatusEffect>bironas_timewarp_rls</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- LoreOfMetal -->
    <TriggeredEffectTemplate StringID="final_transmutation"
                             BurstParticleEffectPrefab="final_transmutation_ground"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             ImbuedStatusEffectDuration="35"
                             DamageAmount="0"
                             Radius="12"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="2">
        <ImbuedStatusEffect>final_transmutation_dot</ImbuedStatusEffect>
        <ImbuedStatusEffect>final_transmutation_mov_90</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="golden_hounds_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Fire"
                             DamageAmount="40"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.15"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="meteoric_ironclad"
                             BurstParticleEffectPrefab=""
                             SoundEffectId=""
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="0"
                             Radius="20"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="45"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>ironclad_phys_res</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="plague_of_rust"
                             BurstParticleEffectPrefab=""
                             SoundEffectId=""
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="0"
                             Radius="17"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="120"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>plague_of_rust_hex</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="golden_globe_explosion"
                             BurstParticleEffectPrefab="golden_explosion"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="75"
                             Radius="5"
                             HasShockWave="true"
                             TargetType="All"
                             ImbuedStatusEffectDuration="20"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>golden_globe_mov_50</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="gleaming_arrow_explosion"
                             BurstParticleEffectPrefab="yellow_particle_explosion"
                             SoundEffectId="dart_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Physical"
                             DamageAmount="100"
                             Radius="2"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="apply_quicksilver_sword_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="25"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="60"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyQuickSilverWeaponItemTraitScript" />
    <!-- LifeMagic -->
    <TriggeredEffectTemplate StringID="dwellers_below_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="8"
                             DamageType="Physical"
                             DamageAmount="10"
                             Radius="6"
                             HasShockWave="true"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="2.1"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>dwellersbelow_impairment</ImbuedStatusEffect>
        <ImbuedStatusEffect>dwellersbelow_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="bark_skin"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="20"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="80"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>barkskin</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="summer_heat"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="10"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="15"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>summerheat_impairment_50</ImbuedStatusEffect>
        <ImbuedStatusEffect>summerheat_fireres_debuff_25</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="regrowth_heal"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="7"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="12"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>regrowth_heal</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="drain_life"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="10"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>drain_life_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="storm_of_renewal"
                             BurstParticleEffectPrefab="storm_of_renewal_ground"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="-30"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="2"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>storm_of_renewal_heal</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="green_eye_end"
                             BurstParticleEffectPrefab="green_eye_end"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="50"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="2"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect />
    </TriggeredEffectTemplate>
	    <TriggeredEffectTemplate StringID="shield_of_thorns"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="20"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="80"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>shield_of_thorns</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Beast Magic -->
    <TriggeredEffectTemplate StringID="tangling_thorn"
                             BurstParticleEffectPrefab="tangling_thorn_ground"
                             SoundEffectId=""
                             SoundEffectLength="1"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="10"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>tangling_thorn</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="pans_pelt"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="40"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>pans_pelt</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="flock_of_doom"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             ImbuedStatusEffectDuration="1"
                             DamageAmount="0"
                             Radius="8"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="2">
        <ImbuedStatusEffect>flock_of_doom_dot</ImbuedStatusEffect>
        <ImbuedStatusEffect>flock_of_doom_phy_red</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="beast_unleashed"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             ImbuedStatusEffectDuration="60"
                             DamageAmount="0"
                             Radius="20"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>beast_unleashed_phys_buff</ImbuedStatusEffect>
        <ImbuedStatusEffect>beast_unleashed_ats_buff</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="curse_of_anraheir"
                             BurstParticleEffectPrefab="curse_of_anraheir_ground"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             ImbuedStatusEffectDuration="1"
                             DamageAmount="0"
                             Radius="8"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="2">
        <ImbuedStatusEffect>curse_of_anraheir_ats_debuff</ImbuedStatusEffect>
        <ImbuedStatusEffect>curse_of_anraheir_mv_debuff</ImbuedStatusEffect>
        <ImbuedStatusEffect>curse_of_anraheir_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="fiery_convocation"
                             BurstParticleEffectPrefab="fiery_convocation"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Fire"
                             DamageAmount="70"
                             Radius="8"
                             HasShockWave="true"
                             TargetType="All"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="10"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fireball_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="courage_of_aenarion"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="12"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="40"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>courage_of_aenarion_ats</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="curse_of_arrows"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="10"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>curse_of_arrows</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Necromancy -->
    <TriggeredEffectTemplate StringID="nagashgaze_explosion"
                             BurstParticleEffectPrefab="purple_particle_explosion"
                             SoundEffectId="dart_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="55"
                             Radius="2"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="shadowblood_explosion"
                             BurstParticleEffectPrefab="shadowblood_explosion"
                             SoundEffectId="blood_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="40"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="breath_of_darkness"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="6"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="10"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>breath_of_darkness</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="witheringwave_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="75"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.15"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="summon_skeleton"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="30"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             TroopIdToSummon="tor_vc_skeleton_warrior"
                             NumberToSummon="25"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.SummonScript" />
    <TriggeredEffectTemplate StringID="summon_champion"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="1"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0"
                             ImbuedStatusEffectDuration="1"
                             TroopIdToSummon="tor_vc_harbinger_champion"
                             NumberToSummon="1">
        <ImbuedStatusEffect>greater_harbinger_debuff</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="summon_dryads"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="1"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0"
                             ImbuedStatusEffectDuration="1"
                             TroopIdToSummon="tor_we_dryad"
                             NumberToSummon="3" />
    <TriggeredEffectTemplate StringID="summon_treeman"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="1"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0"
                             ImbuedStatusEffectDuration="1"
                             TroopIdToSummon="tor_we_treeman"
                             NumberToSummon="1" />
    <TriggeredEffectTemplate StringID="summon_kandorak"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             TroopIdToSummon="tor_vc_grave_guard_warrior"
                             NumberToSummon="4"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.SummonScript" />
    <TriggeredEffectTemplate StringID="summon_gravecall"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="30"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             TroopIdToSummon="tor_vc_grave_guard_warrior"
                             NumberToSummon="25"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.SummonScript" />
    <TriggeredEffectTemplate StringID="wind_of_death_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="40"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="curse_of_years_hex"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="10"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="30"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>curseofyears_movementimpair_50</ImbuedStatusEffect>
        <ImbuedStatusEffect>curseofyears_attackspeed_50</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Dark Magic -->
    <TriggeredEffectTemplate StringID="deathspasm_explosion"
                             BurstParticleEffectPrefab="psys_fireball_explosion_1"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="80"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             ImbuedStatusEffectDuration="5"
                             DamageVariance="0.05"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fireball_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="soulstealer"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="12"
                             DamageVariance="0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>soulstealer</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="chillwind_burst"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="65"
                             Radius="12"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none"
                             HasShockWave="false" />
    <TriggeredEffectTemplate StringID="screaming_skull_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="60"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="soulrain_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Lightning"
                             DamageAmount="45"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="power_of_darkness_bonus"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="6"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="35"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>physical_bonus_20</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- High Magic -->
    <TriggeredEffectTemplate StringID="apotheosis_heal"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="3"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>apotheosis_heal</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="soul_quench_explosion"
                             BurstParticleEffectPrefab="soul_quench_explosion"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="50"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="3"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fireball_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Artillery -->
    <TriggeredEffectTemplate StringID="place_mortar"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.PrefabSpawnerScript"
                             SpawnPrefabName="tor_mortar" />
    <TriggeredEffectTemplate StringID="place_greatcannon"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.PrefabSpawnerScript"
                             SpawnPrefabName="tor_greatcannon" />
    <TriggeredEffectTemplate StringID="place_fieldtrebuchet"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.PrefabSpawnerScript"
                             SpawnPrefabName="tor_fieldtrebuchet" />
    <!-- Sigmarite Prayers -->
    <TriggeredEffectTemplate StringID="comet_of_sigmar_explosion"
                             BurstParticleEffectPrefab="psys_burning_projectile_default_coll"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Holy"
                             DamageAmount="30"
                             Radius="5"
                             HasShockWave="true"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="0"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="healing_hand_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>healing_hand_tick</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_swift_shiver_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="20"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplySwiftShiverTrait" />
    <TriggeredEffectTemplate StringID="apply_hagbane_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="3"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyHagbaneTrait" />
    <TriggeredEffectTemplate StringID="apply_starfire_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="6"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyStarFireTrait" />
    <TriggeredEffectTemplate StringID="apply_flaming_sword_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="25"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="45"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyFlamingItemTraitScript" />
    <TriggeredEffectTemplate StringID="armour_of_the_righteous"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>armour_of_the_righteous</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Ulrican Prayers -->
    <TriggeredEffectTemplate StringID="ulrics_gift"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="25"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>ulrics_gift_mvs</ImbuedStatusEffect>
        <ImbuedStatusEffect>ulrics_gift_ats</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="heart_of_the_wolf"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="10"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="20"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>heart_of_the_wolf_dmg</ImbuedStatusEffect>
        <ImbuedStatusEffect>heart_of_the_wolf_unb</ImbuedStatusEffect>
        <ImbuedStatusEffect>heart_of_the_wolf_uns</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="ice_storm"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Frost"
                             DamageAmount="10"
                             Radius="12"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="10"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>ice_storm_mvs</ImbuedStatusEffect>
        <ImbuedStatusEffect>ice_storm_ats</ImbuedStatusEffect>
        <ImbuedStatusEffect>ice_storm_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="snow_king_decree"
                             BurstParticleEffectPrefab="snow_king_decree_explosion"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Frost"
                             DamageAmount="30"
                             Radius="8"
                             HasShockWave="true"
                             TargetType="Enemy"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="15"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>ice_storm_mvs</ImbuedStatusEffect>
        <ImbuedStatusEffect>ice_storm_ats</ImbuedStatusEffect>
        <ImbuedStatusEffect>ice_storm_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Power of the Lady -->
    <TriggeredEffectTemplate StringID="ladys_favour"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>ladysfavour_tick</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="aura_of_lady"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="8"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="15"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>aura_of_lady_magic</ImbuedStatusEffect>
        <ImbuedStatusEffect>aura_of_lady_fire</ImbuedStatusEffect>
        <ImbuedStatusEffect>aura_of_lady_lightning</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="shield_of_combat"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="8"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="15"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>shield_of_combat</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="aerial_shield"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="8"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.0"
                             ImbuedStatusEffectDuration="1.05"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>aerial_shield_tick</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <!-- Misc/Unused -->
    <TriggeredEffectTemplate StringID="explosion_01"
                             BurstParticleEffectPrefab="psys_burning_projectile_default_coll"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="50"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             ImbuedStatusEffectDuration="5"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fireball_dot</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="treeman_smash"
                             BurstParticleEffectPrefab="treeman_smash"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="200"
                             Radius="5"
                             HasShockWave="true"
                             DoNotAlignParticleEffectPrefabOnImpact="true"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="0"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="grenade_explosion"
                             BurstParticleEffectPrefab="psys_grenade_explosion_1"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Fire"
                             DamageAmount="50"
                             Radius="5"
                             HasShockWave="true"
                             TargetType="All"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="healing_tick"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="-20"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Friendly"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="shadow_step"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="0"
                             Radius="1.5"
                             HasShockWave="false"
                             TargetType="Self"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.KnockDownScript" />
    <TriggeredEffectTemplate StringID="apply_blastofagony"
                             BurstParticleEffectPrefab="blast_of_agony_explosion"
                             SoundEffectId="fireball_explosion"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="80"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="All"
                             ImbuedStatusEffectDuration="5"
                             DamageVariance="0.05"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="summon_wraith"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="All"
                             DamageVariance="0"
                             TroopIdToSummon="tor_vc_spirit_host"
                             NumberToSummon="1"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.SummonScript" />
    <TriggeredEffectTemplate StringID="apply_arcaneconduit"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="10"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>arcane_conduit_slow</ImbuedStatusEffect>
        <ImbuedStatusEffect>arcane_conduit_res_debuff</ImbuedStatusEffect>
        <ImbuedStatusEffect>arcane_conduit_winds_reg</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_knightly_charge"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>knightly_charge_lsc</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_holy_grail_lance_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="8"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyHolyItemTraitScript" />
    <TriggeredEffectTemplate StringID="apply_red_fury"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>redfury_effect_dmg</ImbuedStatusEffect>
        <ImbuedStatusEffect>redfury_effect_res</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_let_them_have_it"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>let_them_have_it_unstoppable</ImbuedStatusEffect>
        <ImbuedStatusEffect>let_them_have_it_unbreakable</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_arrow_of_kurnous"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="35"
                             Radius="1"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="10"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="ulric_smash"
                             BurstParticleEffectPrefab="treeman_smash"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="50"
                             Radius="2"
                             HasShockWave="false"
                             DoNotAlignParticleEffectPrefabOnImpact="true"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="6"
                             DamageVariance="0.2"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="ulric_smash_knockdown"
                             BurstParticleEffectPrefab="treeman_smash"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="50"
                             Radius="3"
                             HasShockWave="true"
                             DoNotAlignParticleEffectPrefabOnImpact="true"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="6"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="apply_righteous_fury"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>righteous_fury_effect</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_accusation"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="8"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>accusation_debuff</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_accusation_selfbuff"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="8"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>accusation_buff_penetration</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_mistwalk"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="10"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="3"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>mistwalk_base</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_mistwalk_area"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="10"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>mistwalk_healing_group</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_vanhelsdansemacabre"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="10"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="60"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>vanhelsdansemacabre_ats</ImbuedStatusEffect>
        <ImbuedStatusEffect>vanhelsdansemacabre_mvs</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_righteous_fury_pulse"
                             BurstParticleEffectPrefab="righteous_fury_dmg_pulse"
                             SoundEffectId="none"
                             SoundEffectLength="1.98"
                             DamageType="Holy"
                             DamageAmount="7"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Enemy"
                             ImbuedStatusEffectDuration="1.98"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="apply_fury_sword_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="2.1"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyHolyItemTraitScript" />
    <TriggeredEffectTemplate StringID="amber_spear"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="60"
                             HasShockWave="false"
							 Radius="0.5"
                             TargetType="Enemy"
                             DamageVariance="0" />
    <TriggeredEffectTemplate StringID="attackspeed_bonus_20"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="15"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>attackspeed_bonus_20</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_fire_cloak_augment"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="30"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fire_cloak_phy_res</ImbuedStatusEffect>
        <ImbuedStatusEffect>fire_cloak_fire_res</ImbuedStatusEffect>
        <ImbuedStatusEffect>fire_cloak_thorns</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="ward_of_arrows"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="10"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>ward_of_arrows</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="fey_paths_self"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Self"
                             ImbuedStatusEffectDuration="6"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fey_path_res</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="fey_paths_enemy"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Magical"
                             DamageAmount="1"
                             Radius="5"
                             HasShockWave="true"
                             TargetType="Enemy"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="fey_paths_friendly"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="5"
                             DamageVariance="0.0"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>fey_path_dmg</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="apply_mindcontrol"
                             BurstParticleEffectPrefab="shadowblood_explosion"
                             SoundEffectId="none"
                             SoundEffectLength="2.5"
                             DamageType="Magical"
                             DamageAmount="0"
                             Radius="3"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="0.1"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="apply_fellfang_fire"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Fire"
                             DamageAmount="40"
                             Radius="0"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="0"
                             ScriptNameToTrigger="none" />
    <TriggeredEffectTemplate StringID="apply_fellfang_explosion"
                             BurstParticleEffectPrefab="psys_fireball_explosion_1"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Fire"
                             DamageAmount="20"
                             Radius="5"
                             HasShockWave="false"
                             TargetType="Enemy"
                             DamageVariance="0"
                             ScriptNameToTrigger="none" />
    <!-- Powerstones -->
    <TriggeredEffectTemplate StringID="powerstone_fire_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="30"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyLesserFlamingItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_fire_amp"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_fire_amp_1</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_fire_res"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_fire_res</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_fire_res_frost</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_fire_trait2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Invalid"
                             DamageAmount="0"
                             Radius="4"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="30"
                             DamageVariance="0"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyFlamingItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_fire_amp_mov"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_fire_amp_1</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_fire_mov</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_fire_amp3"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="ApplyLesserFlamingItemTraitScript">
        <ImbuedStatusEffect>powerstone_fire_amp3</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_light_res"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_light_res_1</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_light_mov"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_light_mov</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_light_res2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_light_res_mag</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_light_res_2</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_light_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyLesserLightItemTraitScript">
        <ImbuedStatusEffect>powerstone_light_mov</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_light_trait2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyLightItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_beast_res_range"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_beast_res_range</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_beast_res"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_beast_res</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_beast_dmg_range"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_beast_dmg_range</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_beast_wild"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_beast_wild</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_beast_range_res_hunt"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_beast_res_range2</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_beast_dmg_range2</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_beast_range_res2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_beast_res2</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_beast_mov</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_res_mag"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_life_res_fire</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_life_res_mag</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_res"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>armour_of_the_righteous</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_res_phys"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_life_res_phys</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_res_debuff"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_life_res_phys2</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_life_mov_debuff</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_thorns"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_life_thorns</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_res_ward"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_life_res_ward</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_life_reg"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_life_reg</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_heavens_dmg_range"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_heavens_dmg_range</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_heavens_res"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_heavens_res_mag</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_heavens_res_elec</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_heavens_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyLesserHeavensItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_heavens_trait2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyHeavensItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_heavens_trait3"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyGreaterHeavensItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_heavens_res2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_heavens_res_mag2</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_heavens_res_elec2</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_heavens_res</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_metal_dmg1"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_metal_dmg1</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_metal_dmg2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_metal_dmg2</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_metal_res_less"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_metal_res</ImbuedStatusEffect>
        <ImbuedStatusEffect>powerstone_metal_res_debuff</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_metal_trait"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyMetalItemTraitScript" />
    <TriggeredEffectTemplate StringID="powerstone_metal_pen"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="none">
        <ImbuedStatusEffect>powerstone_metal_pen</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
    <TriggeredEffectTemplate StringID="powerstone_metal_trait2"
                             BurstParticleEffectPrefab="none"
                             SoundEffectId="none"
                             SoundEffectLength="0"
                             DamageType="Physical"
                             DamageAmount="0"
                             Radius="15"
                             HasShockWave="false"
                             TargetType="Friendly"
                             ImbuedStatusEffectDuration="8"
                             ScriptNameToTrigger="TOR_Core.BattleMechanics.TriggeredEffect.Scripts.ApplyMetalItemTraitScript">
        <ImbuedStatusEffect>powerstone_metal_pen</ImbuedStatusEffect>
    </TriggeredEffectTemplate>
</TriggeredEffectTemplates>