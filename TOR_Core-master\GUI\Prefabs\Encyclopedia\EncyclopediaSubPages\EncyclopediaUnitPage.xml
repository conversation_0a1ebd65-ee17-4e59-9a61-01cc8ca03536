<Prefab>
  <Constants>

    <Constant Name="Encyclopedia.Canvas.Width" BrushName="Encyclopedia.Canvas" BrushLayer="Default" BrushValueType="Width"/>
    <Constant Name="Encyclopedia.Canvas.Height" BrushName="Encyclopedia.Canvas" BrushLayer="Default" BrushValueType="Height"/>

    <Constant Name="Encyclopedia.Width" Value="!Encyclopedia.Canvas.Width" Additive="-35"/>
    <Constant Name="Encyclopedia.Height" Value="!Encyclopedia.Canvas.Height" Additive="-198"/>

  </Constants>

  <Window>
    <BrushWidget HeightSizePolicy ="StretchToParent" WidthSizePolicy="StretchToParent" DoNotAcceptEvents="true" Brush="Encyclopedia.Page.SoundBrush">
      <Children>

        <Widget HeightSizePolicy ="Fixed" WidthSizePolicy="Fixed" SuggestedHeight="!Encyclopedia.Height" SuggestedWidth="!Encyclopedia.Width" HorizontalAlignment="Center" VerticalAlignment="Center" MarginTop="155"  >
          <Children>

            <ListPanel HeightSizePolicy="StretchToParent" WidthSizePolicy="StretchToParent" DoNotAcceptEvents="true" >
              <Children>

                <!--Left Side Faction Image Properties-->
                <Widget HeightSizePolicy ="StretchToParent" WidthSizePolicy="Fixed" SuggestedWidth="370">
                  <Children>

                    <Widget WidthSizePolicy="Fixed" SuggestedWidth="370" HeightSizePolicy ="StretchToParent" HorizontalAlignment="Left" MarginBottom="1" VerticalAlignment="Center" IsEnabled="false" ClipContents="true">
                      <Children>
                        <ParallaxItemBrushWidget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="582" SuggestedHeight="380" Brush="Encyclopedia.Character.Smoke" OneDirectionDuration="5" OneDirectionDistance="100" InitialDirection="Right" IsEaseInOutEnabled="true" VerticalAlignment="Bottom" HorizontalAlignment="Center" PositionYOffset="1" />
                        <ParallaxItemBrushWidget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="582" SuggestedHeight="380" Brush="Encyclopedia.Character.Smoke2" OneDirectionDuration="5" OneDirectionDistance="100" InitialDirection="Left" IsEaseInOutEnabled="true" VerticalAlignment="Bottom" HorizontalAlignment="Center" PositionYOffset="1" />
                        <CharacterTableauWidget DataSource="{UnitCharacter}" WidthSizePolicy="StretchToParent"  HeightSizePolicy ="StretchToParent" HorizontalAlignment="Center" VerticalAlignment="Center" BodyProperties="@BodyProperties" IsFemale="@IsFemale" EquipmentCode="@EquipmentCode" CharStringId="@CharStringId" StanceIndex="@StanceIndex" BannerCodeText="@BannerCodeText" MountCreationKey="@MountCreationKey" IsEnabled="false" ArmorColor1="@ArmorColor1" ArmorColor2="@ArmorColor2" Race="@Race"/>
                      </Children>
                    </Widget>

                    <ListPanel WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" MarginTop="10" StackLayout.LayoutMethod="VerticalBottomToTop">
                      <Children>

                        <!--Unit Name-->
                        <RichTextWidget WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" HorizontalAlignment="Center" MarginLeft="50" MarginRight="50" Brush="Encyclopedia.SubPage.Title.Text" Brush.TextHorizontalAlignment="Center" Brush.TextVerticalAlignment="Top" Text="@NameText"/>
                        <!--Widget WidthSizePolicy="StretchToParent" HeightSizePolicy="Fixed" SuggestedHeight="4" MarginLeft="2" Sprite="Encyclopedia\list_filters_divider" /-->

                        <ListPanel DataSource="{PropertiesList}" WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" HorizontalAlignment="Center" MarginTop="5">
                          <ItemTemplate>
                            <Widget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="32" SuggestedHeight="32" HorizontalAlignment="Right" VerticalAlignment="Top" PositionXOffset="-1" PositionYOffset="2" MarginLeft="10" Sprite="@Text">
                              <Children>
                                <HintWidget DataSource="{Hint}" DoNotAcceptEvents="true" WidthSizePolicy="StretchToParent" HeightSizePolicy="StretchToParent" Command.HoverBegin="ExecuteBeginHint" Command.HoverEnd="ExecuteEndHint"/>
                              </Children>
                            </Widget>
                          </ItemTemplate>
                        </ListPanel>
                        
						<!--TOR specific information-->
						<Widget DoNotPassEventsToChildren="false" WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="35" SuggestedHeight="35" HorizontalAlignment="Left" VerticalAlignment="Center" MarginLeft="15" Sprite="extendedinfo_icon_100" IsVisible="true">
						  <Children>
							<HintWidget DataSource="{ExtendedInfoHint}" WidthSizePolicy="StretchToParent" HeightSizePolicy="StretchToParent" Command.HoverBegin="ExecuteBeginHint" Command.HoverEnd="ExecuteEndHint" />
						  </Children>
						</Widget>
                        <ListPanel WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" MarginTop="5" DataSource="{CurrentSelectedEquipmentSet}">
                          <Children>
                            
                            <!-- Left Equipment List (Armors & Horse) -->
                            <NavigationScopeTargeter ScopeID="EncyclopediaUnitLeftEquipmentListScope" ScopeParent="..\LeftEquipmentList" ScopeMovements="Vertical" />
                            <NavigatableListPanel Id="LeftEquipmentList" DataSource="{LeftEquipmentList}" WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" StackLayout.LayoutMethod="VerticalBottomToTop">
                              <ItemTemplate>
                                <EquipmentTypeVisualBrushWidget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="35" SuggestedHeight="35" HorizontalAlignment="Left" MarginLeft="15" MarginBottom="5" Brush="EquipmentType.Image" Command.HoverBegin="ExecuteBeginHint" Command.HoverEnd="ExecuteEndHint" IsVisible="@HasItem" Type="@Type" />
                              </ItemTemplate>
                            </NavigatableListPanel>
                            
                            <!-- Right Equipment List (Weapons) -->
                            <NavigationScopeTargeter ScopeID="EncyclopediaUnitRightEquipmentListScope" ScopeParent="..\RightEquipmentList" ScopeMovements="Vertical" />
                            <NavigatableListPanel Id="RightEquipmentList" DataSource="{RightEquipmentList}" WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" StackLayout.LayoutMethod="VerticalBottomToTop">
                              <ItemTemplate>
                                <EquipmentTypeVisualBrushWidget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="35" SuggestedHeight="35" HorizontalAlignment="Right" MarginRight="15" MarginBottom="5" Brush="EquipmentType.Image" Command.HoverBegin="ExecuteBeginHint" Command.HoverEnd="ExecuteEndHint" IsVisible="@HasItem" Type="@Type" />
                              </ItemTemplate>
                            </NavigatableListPanel>
							
                          </Children>
                        </ListPanel>

		
						
						
                      </Children>
                    </ListPanel>

                    <!--Skills Grid-->
                    <!--<GridWidget DataSource="{Skills}" WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" SuggestedWidth="530" SuggestedHeight="350" DefaultCellWidth="55" DefaultCellHeight="70" HorizontalAlignment="Center" VerticalAlignment="Bottom" ColumnCount="7" MarginBottom="0" MarginLeft="40" LayoutImp.VerticalLayoutMethod="TopToBottom">
                      <ItemTemplate>
                        <SkillIconVisualWidget WidthSizePolicy="StretchToParent" HeightSizePolicy="StretchToParent" Sprite="@FileName" MarginBottom="20" UseSmallestVariation="true" MarginLeft="5">
                          <Children>
                            <TextWidget WidthSizePolicy="StretchToParent" HeightSizePolicy="Fixed" SuggestedHeight="20" IntText="@SkillValue" VerticalAlignment="Bottom" Brush="Encyclopedia.Skill.Text" Brush.FontSize="20" PositionYOffset="20"/>
                            <HintWidget DataSource="{Hint}" HeightSizePolicy ="StretchToParent" WidthSizePolicy="StretchToParent" Command.HoverBegin="ExecuteBeginHint" Command.HoverEnd="ExecuteEndHint"/>
                          </Children>
                        </SkillIconVisualWidget>
                      </ItemTemplate>
                    </GridWidget>-->

                    <Widget WidthSizePolicy="Fixed" HeightSizePolicy="StretchToParent" SuggestedWidth="2" HorizontalAlignment="Right" Sprite="Encyclopedia\divider_vertical" />

                    <!--Bookmark Button-->
                    <NavigationScopeTargeter ScopeID="EncyclopediaUnitBookmarkScope" ScopeParent="..\BookmarkButton" />
                    <ButtonWidget Id="BookmarkButton" WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="50" SuggestedHeight="50" HorizontalAlignment="Left" VerticalAlignment="Top" MarginLeft="5" MarginTop="5" Brush="Encyclopedia.Bookmark.Button" Command.Click="ExecuteSwitchBookmarkedState" IsSelected="@IsBookmarked" GamepadNavigationIndex="0">
                      <Children>
                        <HintWidget DataSource="{BookmarkHint}" WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" Command.HoverBegin="ExecuteBeginHint" Command.HoverEnd="ExecuteEndHint"/>
                      </Children>
                    </ButtonWidget>

                  </Children>
                </Widget>
                
                <Widget HeightSizePolicy="StretchToParent"  WidthSizePolicy="StretchToParent">
                  <Children>
                    
                    <EncyclopediaTroopScrollablePanel Id="RightSideScrollablePanel" HeightSizePolicy ="StretchToParent" WidthSizePolicy="StretchToParent" InnerPanel="RightSideRect\InnerPanel" ClipRect="RightSideRect" VerticalAlignment="Center" HorizontalAlignment="Center" HorizontalScrollbar="..\RightSideHorizontalScrollbar" VerticalScrollbar="..\RightSideVerticalScrollbar" PanWithMouseEnabled="true" AutoHideScrollBars="true" MarginTop="10" MarginBottom="85" MarginLeft="0" MarginRight="0">
                      <Children>

                        <!--Right Side Character Clan, Friends, Enemies-->
                        <Widget Id="RightSideRect" HeightSizePolicy ="StretchToParent" WidthSizePolicy="StretchToParent" HorizontalAlignment="Center" DoNotAcceptEvents="true" ClipContents="true" MarginLeft="0" MarginRight="0" MarginBottom="-85">
                          <Children>

                            <Widget Id="InnerPanel" HeightSizePolicy="CoverChildren" WidthSizePolicy="CoverChildren" HorizontalAlignment="Center" IsHidden="@HasErrors" >
                              <Children>
                                
                                <!--Center Side-->
                                <Widget DataSource="{Tree}" HorizontalAlignment="Center" HeightSizePolicy="CoverChildren" WidthSizePolicy="CoverChildren" MarginTop="10" MarginBottom="65">
                                  <Children>

                                    <EncyclopediaUnitTreeNodeItem Id="InnerPanel" HorizontalAlignment="Center" DoNotAcceptEvents="true"/>

                                  </Children>
                                </Widget>
                                
                              </Children>
                            </Widget>
                            
                          </Children>
                        </Widget>

                      </Children>
                    </EncyclopediaTroopScrollablePanel>

                    <Widget WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" MarginTop="50" IsVisible="@HasErrors">
                      <Children>
                        <TextWidget WidthSizePolicy="StretchToParent" HeightSizePolicy="CoverChildren" Brush="Encyclopedia.Error.Text" Text="@TreeDisplayErrorText" />
                      </Children>
                    </Widget>
                    
                    <!--Bottom Shadow-->
                    <Widget HeightSizePolicy ="Fixed" WidthSizePolicy="StretchToParent" SuggestedHeight="158" Sprite="StdAssets\scroll_hide" VerticalAlignment="Bottom" IsEnabled="false"/>
                    
                    <ScrollbarWidget HeightSizePolicy ="StretchToParent" WidthSizePolicy="Fixed" Id="RightSideVerticalScrollbar" SuggestedWidth="30" MinValue="0" MaxValue="100" MarginRight="10" MarginBottom="70" MarginTop="0" AlignmentAxis="Vertical" HorizontalAlignment="Right" VerticalAlignment="Center" Handle="ScrollbarHandle" UpdateChildrenStates="true" >
                      <Children>
                        <Widget Id="ScrollbarBed" WidthSizePolicy="Fixed" HeightSizePolicy="StretchToParent" SuggestedWidth="4" HorizontalAlignment="Center"  Sprite="SPGeneral\SPRecruitment\slider_thin_bed_vertical" />
                        <ImageWidget Id="ScrollbarHandle" WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" MinHeight="153" SuggestedWidth="30" SuggestedHeight="150" HorizontalAlignment="Center" Brush="Encyclopedia.Scroll.Handle.Vertical" />
                      </Children>
                    </ScrollbarWidget>
                    
                    <ScrollbarWidget HeightSizePolicy ="Fixed" WidthSizePolicy="StretchToParent" Id="RightSideHorizontalScrollbar" SuggestedHeight="8" MinValue="0" MaxValue="100" MarginLeft="40" MarginRight="55" MarginBottom="65" AlignmentAxis="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Bottom" Handle="ScrollbarHandle" UpdateChildrenStates="true" >
                      <Children>
                        <Widget WidthSizePolicy="StretchToParent" HeightSizePolicy="Fixed" SuggestedHeight="4" VerticalAlignment="Center" Sprite="SPGeneral\SPRecruitment\slider_thin_bed_horizontal" />
										    <ImageWidget Id="ScrollbarHandle" WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" MinWidth="153" SuggestedHeight="30" SuggestedWidth="150" VerticalAlignment="Center" Brush="Encyclopedia.Scroll.Handle.Horizontal" />
                      </Children>
                    </ScrollbarWidget>
                    
                  </Children>
                </Widget>

              </Children>
            </ListPanel>

            <Widget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="370" SuggestedHeight="100" HorizontalAlignment="Left" VerticalAlignment="Bottom" >
              <Children>

                <Widget HeightSizePolicy ="Fixed" WidthSizePolicy="StretchToParent" SuggestedHeight="158" Sprite="StdAssets\scroll_hide" VerticalAlignment="Bottom" IsEnabled="false"/>
                
                <ListPanel DataSource="{EquipmentSetSelector}" WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" HorizontalAlignment="Center" VerticalAlignment="Center" StackLayout.LayoutMethod="HorizontalLeftToRight" MarginBottom="10" >
                  <Children>

                    <ButtonWidget DoNotPassEventsToChildren="true" Command.Click="ExecuteSelectPreviousItem" WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" VerticalAlignment="Center" UpdateChildrenStates="true">
                      <Children>
                        <ListPanel WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" VerticalAlignment="Center" UpdateChildrenStates="true">
                          <Children>
                            <ImageWidget HeightSizePolicy ="Fixed" WidthSizePolicy="Fixed" SuggestedHeight="48" SuggestedWidth="41" VerticalAlignment="Bottom" MarginRight="5" Brush="ButtonRightBigArrowBrush1"/>
                          </Children>
                        </ListPanel>
                      </Children>
                    </ButtonWidget>

                    <TextWidget DataSource="{..}" WidthSizePolicy="Fixed" HeightSizePolicy="CoverChildren" SuggestedWidth="200" VerticalAlignment="Center" Brush="Encyclopedia.EquipmentSet.Text" Text="@EquipmentSetText" />

                    <ButtonWidget DoNotPassEventsToChildren="true" Command.Click="ExecuteSelectNextItem" WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" VerticalAlignment="Center" UpdateChildrenStates="true">
                      <Children>
                        <ListPanel WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" VerticalAlignment="Center" UpdateChildrenStates="true">
                          <Children>
                            <ImageWidget HeightSizePolicy ="Fixed" WidthSizePolicy="Fixed" SuggestedHeight="48" SuggestedWidth="41" VerticalAlignment="Bottom" MarginRight="5" Brush="ButtonLeftBigArrowBrush1"/>
                          </Children>
                        </ListPanel>
                      </Children>
                    </ButtonWidget>

                  </Children>
                </ListPanel>
                
              </Children>
            </Widget>
            
            <!--Previous And Next Buttons-->
            <EncyclopediaQuickNavigation/>

          </Children>
        </Widget>

      </Children>
    </BrushWidget>
  </Window>
</Prefab>
