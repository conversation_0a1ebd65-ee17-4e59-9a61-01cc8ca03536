using System;
using System.Xml.Serialization;
using TOR_Core.AbilitySystem;

namespace LOL_Core.HeroSkillSystem.Core
{
    [Serializable]
    public class HeroAbilityTemplate : AbilityTemplate
    {
        [XmlAttribute]
        public float BaseDamage { get; set; } = 50f;
        
        [XmlAttribute]
        public bool CanGrow { get; set; } = false;
        
        [XmlAttribute]
        public float GrowthPerKill { get; set; } = 1f;
        
        [XmlAttribute]
        public int RequiredLevel { get; set; } = 1;
        
        [XmlAttribute]
        public string Description { get; set; } = "";
        
        [XmlAttribute]
        public string HeroID { get; set; } = "";
        
        [XmlAttribute]
        public HeroSkillType SkillType { get; set; } = HeroSkillType.Active;
        
        [XmlAttribute]
        public float Range { get; set; } = 2f;
        
        [XmlAttribute]
        public bool RequiresTarget { get; set; } = true;

        public HeroAbilityTemplate() : base()
        {
            AbilityType = AbilityType.Spell; // 使用现有的类型
        }

        public HeroAbilityTemplate(string id) : base(id)
        {
            AbilityType = AbilityType.Spell; // 使用现有的类型
        }

        public new ITemplate Clone(string newId)
        {
            return new HeroAbilityTemplate(newId)
            {
                Name = Name,
                SpriteName = SpriteName,
                CoolDown = CoolDown,
                Duration = Duration,
                Radius = Radius,
                AbilityType = AbilityType,
                AbilityEffectType = AbilityEffectType,
                BaseMovementSpeed = BaseMovementSpeed,
                TickInterval = TickInterval,
                TriggerType = TriggerType,
                TriggeredEffects = TriggeredEffects,
                HasLight = HasLight,
                LightIntensity = LightIntensity,
                LightRadius = LightRadius,
                LightColorRGB = LightColorRGB,
                LightFlickeringMagnitude = LightFlickeringMagnitude,
                LightFlickeringInterval = LightFlickeringInterval,
                ShadowCastEnabled = ShadowCastEnabled,
                ParticleEffectPrefab = ParticleEffectPrefab,
                ParticleEffectSizeModifier = ParticleEffectSizeModifier,
                SoundEffectToPlay = SoundEffectToPlay,
                ShouldSoundLoopOverDuration = ShouldSoundLoopOverDuration,
                CastType = CastType,
                CastTime = CastTime,
                AnimationActionName = AnimationActionName,
                AbilityTargetType = AbilityTargetType,
                Offset = Offset,
                CrosshairType = CrosshairType,
                MinDistance = MinDistance,
                MaxDistance = MaxDistance,
                TargetCapturingRadius = TargetCapturingRadius,
                TooltipDescription = TooltipDescription,
                MaxRandomDeviation = MaxRandomDeviation,
                ShouldRotateVisuals = ShouldRotateVisuals,
                VisualsRotationVelocity = VisualsRotationVelocity,
                ScaleVariable1 = ScaleVariable1,
                BaseDamage = BaseDamage,
                CanGrow = CanGrow,
                GrowthPerKill = GrowthPerKill,
                RequiredLevel = RequiredLevel,
                Description = Description,
                HeroID = HeroID,
                SkillType = SkillType,
                Range = Range,
                RequiresTarget = RequiresTarget
            };
        }
    }

    public enum HeroSkillType
    {
        Active,
        Passive,
        Ultimate
    }
}
