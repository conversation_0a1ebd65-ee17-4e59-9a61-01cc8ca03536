using TaleWorlds.MountAndBlade;
using TaleWorlds.InputSystem;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.Localization;
using LOL_Core.Extensions;
using LOL_Core.HeroSkillSystem.Core;

namespace LOL_Core.MissionLogics
{
    public class HeroSkillMissionLogic : MissionLogic
    {
        private Agent _playerAgent;
        private HeroAbilityComponent _playerSkillComponent;

        public override void OnBehaviorInitialize()
        {
            base.OnBehaviorInitialize();
        }

        public override void AfterStart()
        {
            base.AfterStart();
            InitializePlayerSkills();
        }

        private void InitializePlayerSkills()
        {
            _playerAgent = Agent.Main;
            if (_playerAgent != null)
            {
                _playerSkillComponent = _playerAgent.GetHeroAbilityComponent();
                if (_playerSkillComponent == null)
                {
                    _playerSkillComponent = new HeroAbilityComponent(_playerAgent);
                    _playerAgent.AddComponent(_playerSkillComponent);
                }
            }
        }

        public override void OnMissionTick(float dt)
        {
            base.OnMissionTick(dt);
            HandleInput();
        }

        private void HandleInput()
        {
            if (_playerAgent == null || _playerSkillComponent == null) return;

            if (Input.IsKeyPressed(InputKey.R))
            {
                TryUseHeroSkill();
            }

            if (Input.IsKeyPressed(InputKey.T))
            {
                ShowSkillInfo();
            }
        }

        private void TryUseHeroSkill()
        {
            if (_playerSkillComponent.HasEquippedSkill())
            {
                var skill = _playerSkillComponent.GetEquippedSkill();
                if (skill != null)
                {
                    if (skill.TryCast(_playerAgent, out var failureReason))
                    {
                        DisplayMessage("技能使用成功！", false);
                    }
                    else
                    {
                        DisplayMessage($"无法使用技能: {failureReason?.ToString() ?? "未知原因"}", true);
                    }
                }
            }
            else
            {
                DisplayMessage("没有装备技能", true);
            }
        }

        private void ShowSkillInfo()
        {
            if (_playerSkillComponent.HasEquippedSkill())
            {
                var skill = _playerSkillComponent.GetEquippedSkill();
                if (skill != null)
                {
                    var description = skill.GetSkillDescription();
                    DisplayMessage(description, false);
                }
            }
            else
            {
                DisplayMessage("没有装备技能", true);
            }
        }

        private void DisplayMessage(string message, bool isWarning)
        {
            if (isWarning)
            {
                InformationManager.DisplayMessage(
                    new InformationMessage(message, Color.FromUint(0xFF0000FF))); // 红色
            }
            else
            {
                InformationManager.DisplayMessage(
                    new InformationMessage(message, Color.FromUint(0x00FF00FF))); // 绿色
            }
        }

        public override void OnAgentRemoved(Agent affectedAgent, Agent affectorAgent, AgentState agentState, KillingBlow killingBlow)
        {
            base.OnAgentRemoved(affectedAgent, affectorAgent, agentState, killingBlow);

            if (agentState == AgentState.Killed && affectorAgent != null && affectorAgent.IsMainAgent)
            {
                var skillComponent = affectorAgent.GetHeroAbilityComponent();
                if (skillComponent != null && skillComponent.HasEquippedSkill())
                {
                    var equippedSkill = skillComponent.GetEquippedSkill();
                    if (equippedSkill != null)
                    {
                        equippedSkill.ProcessKill(affectedAgent, affectorAgent);
                        
                        if (equippedSkill.HeroTemplate.CanGrow)
                        {
                            DisplayMessage($"技能成长！当前层数: {equippedSkill.GrowthData.CurrentStacks}", false);
                        }
                    }
                }
            }
        }

        public void OnHeroSkillKill(HeroAbility skill, Agent killer, Agent victim)
        {
            if (skill.HeroTemplate.CanGrow)
            {
                var skillComponent = killer.GetHeroAbilityComponent();
                skillComponent?.OnSkillKillConfirmed(skill, killer, victim);
            }
        }

        public override void OnMissionModeChange(MissionMode oldMissionMode, bool atStart)
        {
            base.OnMissionModeChange(oldMissionMode, atStart);
            
            if (Mission.Mode == MissionMode.Battle && !atStart)
            {
                InitializePlayerSkills();
            }
        }

        protected override void OnEndMission()
        {
            base.OnEndMission();
            
            if (_playerSkillComponent != null)
            {
                _playerSkillComponent.SaveSkillData();
            }
        }
    }
}
