using TaleWorlds.CampaignSystem;
using TaleWorlds.SaveSystem;
using LOL_Core.Extensions;
using LOL_Core.HeroSkillSystem.Data;

namespace LOL_Core.CampaignBehaviors
{
    public class HeroSkillDataBehavior : CampaignBehaviorBase
    {
        public override void RegisterEvents()
        {
            CampaignEvents.OnGameLoadedEvent.AddNonSerializedListener(this, OnGameLoaded);
            CampaignEvents.OnBeforeSaveEvent.AddNonSerializedListener(this, OnBeforeSave);
        }

        public override void SyncData(IDataStore dataStore)
        {
            // 主要的数据同步在Hero扩展信息中处理
        }

        private void OnGameLoaded(CampaignGameStarter campaignGameStarter)
        {
            // 确保主英雄有技能数据
            if (Hero.MainHero != null)
            {
                var skillData = Hero.MainHero.GetHeroSkillData();
                if (skillData == null)
                {
                    skillData = new HeroSkillData(Hero.MainHero.StringId);
                    Hero.MainHero.SetHeroSkillData(skillData);
                }
            }
        }

        private void OnBeforeSave()
        {
            // 在保存前确保所有技能数据都已更新
            if (Hero.MainHero != null)
            {
                var skillData = Hero.MainHero.GetHeroSkillData();
                if (skillData != null)
                {
                    skillData.LastSaved = System.DateTime.UtcNow;
                    Hero.MainHero.SetHeroSkillData(skillData);
                }
            }
        }
    }
}
