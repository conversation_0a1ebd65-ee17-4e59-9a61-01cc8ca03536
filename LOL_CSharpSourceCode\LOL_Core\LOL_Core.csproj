<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{E1C39013-AA12-41B6-8141-710EB3D84B9B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LOL_Core</RootNamespace>
    <AssemblyName>LOL_Core</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <LangVersion>8.0</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x64</PlatformTarget>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="0Harmony">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\Bannerlord.Harmony\bin\Win64_Shipping_Client\0Harmony.dll</HintPath>
    </Reference>
    <Reference Include="SandBox">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.dll</HintPath>
    </Reference>
    <Reference Include="SandBox.GauntletUI">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.GauntletUI.dll</HintPath>
    </Reference>
    <Reference Include="SandBox.GauntletUI.AutoGenerated.0">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.GauntletUI.AutoGenerated.0.dll</HintPath>
    </Reference>
    <Reference Include="SandBox.GauntletUI.AutoGenerated.1">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.GauntletUI.AutoGenerated.1.dll</HintPath>
    </Reference>
    <Reference Include="SandBox.View">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.View.dll</HintPath>
    </Reference>
    <Reference Include="SandBox.ViewModelCollection">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\SandBox\bin\Win64_Shipping_Client\SandBox.ViewModelCollection.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="TaleWorlds.AchievementSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.AchievementSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.ActivitySystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ActivitySystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.CampaignSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.CampaignSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.CampaignSystem.ViewModelCollection">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.CampaignSystem.ViewModelCollection.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Core">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Core.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Core.ViewModelCollection">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Core.ViewModelCollection.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Diamond">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Diamond.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Diamond.AccessProvider.Epic">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Diamond.AccessProvider.Epic.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Diamond.AccessProvider.GDK">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Diamond.AccessProvider.GDK.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Diamond.AccessProvider.GOG">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Diamond.AccessProvider.GOG.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Diamond.AccessProvider.Steam">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Diamond.AccessProvider.Steam.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Diamond.AccessProvider.Test">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Diamond.AccessProvider.Test.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.DotNet">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.DotNet.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.DotNet.AutoGenerated">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.DotNet.AutoGenerated.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Engine">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Engine.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Engine.AutoGenerated">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Engine.AutoGenerated.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Engine.GauntletUI">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Engine.GauntletUI.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.CodeGenerator">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.CodeGenerator.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.Data">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.Data.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.ExtraWidgets">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.ExtraWidgets.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.PrefabSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.PrefabSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.GauntletUI.TooltipExtensions">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.GauntletUI.TooltipExtensions.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.InputSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.InputSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Library">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Library.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.LinQuick">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.LinQuick.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Localization">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Localization.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.ModuleManager">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ModuleManager.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.AutoGenerated">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.AutoGenerated.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Diamond">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Diamond.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.GauntletUI">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.GauntletUI.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.GauntletUI.AutoGenerated.0">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.GauntletUI.AutoGenerated.0.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.GauntletUI.AutoGenerated.1">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.GauntletUI.AutoGenerated.1.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.GauntletUI.Widgets">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.GauntletUI.Widgets.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Helpers">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Launcher.Library">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Launcher.Library.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Launcher.Steam">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Launcher.Steam.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Multiplayer.Test">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Multiplayer.Test.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.Platform.PC">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.Platform.PC.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.View">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\Modules\Native\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.View.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.MountAndBlade.ViewModelCollection">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.MountAndBlade.ViewModelCollection.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.NavigationSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.NavigationSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Network">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Network.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.ObjectSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ObjectSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.Epic">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.Epic.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.GOG">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.GOG.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.PlatformService.Steam">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.PlatformService.Steam.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.PlayerServices">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.PlayerServices.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.PSAI">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.PSAI.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.SaveSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.SaveSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.ScreenSystem">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ScreenSystem.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.ServiceDiscovery.Client">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.ServiceDiscovery.Client.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.Starter.Library">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.Starter.Library.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.TwoDimension">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.TwoDimension.dll</HintPath>
    </Reference>
    <Reference Include="TaleWorlds.TwoDimension.Standalone">
      <HintPath>..\..\..\Steam\steamapps\common\Mount &amp; Blade II Bannerlord\bin\Win64_Shipping_Client\TaleWorlds.TwoDimension.Standalone.dll</HintPath>
    </Reference>
    <Reference Include="NLog">
      <HintPath>D:\LOL_Core-master\TOR_Core-master\bin\Win64_Shipping_Client\NLog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="TOR_Core">
      <HintPath>..\..\TOR_Core-master\bin\Win64_Shipping_Client\TOR_Core.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CampaignBehaviors\HeroSkillCampaignBehavior.cs" />
    <Compile Include="CampaignBehaviors\HeroSkillDataBehavior.cs" />
    <Compile Include="Extensions\AgentExtensions.cs" />
    <Compile Include="Extensions\HeroExtensions.cs" />
    <Compile Include="HeroSkillSystem\Core\HeroAbility.cs" />
    <Compile Include="HeroSkillSystem\Core\HeroAbilityComponent.cs" />
    <Compile Include="HeroSkillSystem\Core\HeroAbilityFactory.cs" />
    <Compile Include="HeroSkillSystem\Core\HeroAbilityTemplate.cs" />
    <Compile Include="HeroSkillSystem\Core\HeroSkillManager.cs" />
    <Compile Include="HeroSkillSystem\Data\HeroSkillData.cs" />
    <Compile Include="HeroSkillSystem\Data\SkillGrowthData.cs" />
    <Compile Include="HeroSkillSystem\Skills\Nasus\SiphoningStrikeAbility.cs" />
    <Compile Include="HeroSkillSystem\Skills\Nasus\SiphoningStrikeScript.cs" />
    <Compile Include="HeroSkillSystem\UI\HeroSkillEquipScreen.cs" />
    <Compile Include="HeroSkillSystem\UI\HeroSkillEquipVM.cs" />
    <Compile Include="HeroSkillSystem\UI\HeroSkillHUD.cs" />
    <Compile Include="HeroSkillSystem\UI\HeroSkillHUD_VM.cs" />
    <Compile Include="MissionLogics\HeroSkillMissionLogic.cs" />
    <Compile Include="SubModule.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>