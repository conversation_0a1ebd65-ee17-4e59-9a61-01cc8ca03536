using TaleWorlds.CampaignSystem;
using TaleWorlds.CampaignSystem.Actions;
using TaleWorlds.Core;
using TaleWorlds.Library;
using TaleWorlds.Localization;
using TaleWorlds.MountAndBlade;
using LOL_Core.HeroSkillSystem.Core;

namespace LOL_Core.CampaignBehaviors
{
    public class HeroSkillCampaignBehavior : CampaignBehaviorBase
    {
        public override void RegisterEvents()
        {
            CampaignEvents.OnGameLoadedEvent.AddNonSerializedListener(this, OnGameLoaded);
            CampaignEvents.OnNewGameCreatedEvent.AddNonSerializedListener(this, OnNewGameCreated);
            CampaignEvents.HeroLevelledUp.AddNonSerializedListener(this, OnHeroLevelledUp);
            // 注意：MissionStarted 在较新版本的 Bannerlord 中可能不存在，我们在 SubModule 中处理任务逻辑
        }

        public override void SyncData(IDataStore dataStore)
        {
        }

        private void OnGameLoaded(CampaignGameStarter campaignGameStarter)
        {
            InitializeHeroSkillSystem();
        }

        private void OnNewGameCreated(CampaignGameStarter campaignGameStarter)
        {
            InitializeHeroSkillSystem();
            InitializeMainHeroSkills();
        }

        private void InitializeHeroSkillSystem()
        {
            HeroAbilityFactory.LoadTemplates();
        }

        private void InitializeMainHeroSkills()
        {
            if (Hero.MainHero != null)
            {
                HeroSkillManager.InitializeHeroSkills(Hero.MainHero);
            }
        }

        private void OnHeroLevelledUp(Hero hero, bool shouldNotify)
        {
            if (hero == Hero.MainHero)
            {
                CheckForNewSkillUnlocks(hero);
            }
        }

        private void CheckForNewSkillUnlocks(Hero hero)
        {
            var availableSkills = HeroSkillManager.GetAvailableSkillsForHero(hero);
            
            foreach (var skillID in availableSkills)
            {
                var template = HeroAbilityFactory.GetTemplate(skillID);
                if (template != null && hero.Level >= template.RequiredLevel)
                {
                    InformationManager.DisplayMessage(
                        new InformationMessage(
                            $"新技能可用: {template.Name} (等级 {template.RequiredLevel})",
                            Color.FromUint(0xFFD700FF))); // 金色
                }
            }
        }

        // OnMissionStarted 方法已移除，任务逻辑在 SubModule 中处理
    }
}
