<?xml version="1.0" encoding="utf-8"?>
<AbilityTemplates>
    <!-- Minor magic -->
    <AbilityTemplate StringID="Dart"
                     Name="{=dart_label_str}Magic Bolt"
                     SpriteName="dart_icon"
                     CoolDown="10"
                     WindsOfMagicCost="4"
                     BaseMisCastChance="0.05"
                     Duration="300"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="SeekerMissile"
                     BaseMovementSpeed="30"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="dart_particle"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="dart"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     TriggeredEffectID="dart_explosion"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     MaxDistance="100"
                     CrosshairType="SingleTarget"
                     AbilityTargetType="EnemiesInAOE"
                     TooltipDescription="Hurl a magical bolt at the enemy. The missile homes in on the target and does 40 damage in a 3 meter radius on contact.">
        <LightColorRGB x="0"
                       y="204"
                       z="255"
                       w="-1">
            <AsVec2>
                <y>204</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>dart_explosion</TriggeredEffect>
        <SeekerParameters Proportional="0.5"
                          Derivative="0"
                          DisableDistance="10" />
    </AbilityTemplate>
    <AbilityTemplate StringID="WardOfArrows"
                     Name="{=ward_of_arrows_label_str}Ward of Arrows"
                     SpriteName="ward_of_arrows_icon"
                     CoolDown="10"
                     WindsOfMagicCost="3"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="1"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=ward_of_arrows_description_str}Adds 40% physical resistance against ranged damage for 15 seconds to the caster.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>ward_of_arrows</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="MinorHeal"
                     Name="{=minor_heal_label_str}Petty Heal"
                     SpriteName="petty_healing_icon"
                     CoolDown="75"
                     WindsOfMagicCost="10"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Heal"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="1"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=minor_heal_description_str}Heals the caster for 3 health every second for 7 seconds.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_minor_healing_statuseffect</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FoetidCloud"
                     Name="{=foetid_cloud_label_str}Foetid Cloud"
                     SpriteName="foetid_cloud_icon"
                     CoolDown="10"
                     WindsOfMagicCost="4"
                     BaseMisCastChance="0.3"
                     Duration="8"
                     Radius="1"
                     AbilityType="Spell"
                     AbilityEffectType="Blast"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="foetid_cloud"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="gas_cloud_release"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="3"
                     AbilityTargetType="WorldPosition"
                     CrosshairType="Wind"
                     MinDistance="1"
                     MaxDistance="2"
                     TargetCapturingRadius="2"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=foetid_cloud_description_str}A sickly short ranged poisonous gas cloud.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>foetid_cloud_burst</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="EnchantWeapon"
                     Name="{=enchant_weapon_label_str}Enchant Weapon"
                     SpriteName="enchant_weapon_icon"
                     CoolDown="10"
                     WindsOfMagicCost="6"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="SingleAlly"
                     TargetCapturingRadius="1"
                     MaxDistance="20"
                     CrosshairType="SingleTarget"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=enchant_weapon_description_str}Buff a target ally's weapon to do additional magic damage.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>enchant_weapon</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="AuraOfResistance"
                     Name="{=aura_of_resistance_label_str}Aura of Resistance"
                     SpriteName="resistanceaura_icon"
                     CoolDown="10"
                     WindsOfMagicCost="6"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=aura_of_resistance_description_str}Allies in a 3 meter radius gain 20% physical resistance for 15 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>physical_resistance_20</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="StrengthOfCombat"
                     Name="{=strength_of_combat_label_str}Strength Of Combat"
                     SpriteName="healingprayer_icon"
                     CoolDown="10"
                     WindsOfMagicCost="3"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="1"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=strength_of_combat_description_str}Adds 20% physical damage bonus for 15 seconds to the caster.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>physical_bonus_20</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="DustStorm"
                     Name="{=dust_storm_label_str}Dust Storm"
                     SpriteName="dust_storm_icon"
                     CoolDown="10"
                     WindsOfMagicCost="6"
                     BaseMisCastChance="0.3"
                     Duration="8"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Vortex"
                     BaseMovementSpeed="4"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="dust_storm"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="dust_storm"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="3"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="3"
                     MaxDistance="20"
                     TargetCapturingRadius="2"
                     MaxRandomDeviation="2"
                     ShouldRotateVisuals="false"
                     VisualsRotationVelocity="0"
                     SpellTier="1"
                     BelongsToLoreID="MinorMagic"
                     TooltipDescription="{=dust_storm_description_str}Dry winds from the desert knock down victims and apply minor damage.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>dust_storm_tick</TriggeredEffect>
    </AbilityTemplate>
    <!-- Lore of Fire -->
    <AbilityTemplate StringID="Fireball"
                     Name="{=fireball_label_str}Fireball"
                     SpriteName="fireball_icon"
                     CoolDown="25"
                     WindsOfMagicCost="15"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.5"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="35"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="fireball_prefab"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fireball"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=fireball_description_str}There is nothing quite as welcoming as a fireball to the face. Deals 70 damage in a 6 meter radius and burns the foes for 5 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>fireball_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CinderBlast"
                     Name="{=cinder_blast_label_str}Cinder Blast"
                     SpriteName="cinderblast_icon"
                     CoolDown="12"
                     WindsOfMagicCost="7"
                     BaseMisCastChance="0.05"
                     Duration="10"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="40"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="cinder_blast"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="rocks_grinding"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=cinder_blast_description_str}Hurl fragments of solidified lava at the enemy dealing medium damage on contact.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>cinderblast_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="BoltOfAqshy"
                     Name="{=bolt_of_aqshy_label_str}Bolt of Aqshy"
                     SpriteName="boltofashqy_icon"
                     CoolDown="12"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="50"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="10"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="bolt_of_aqshy"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fire_cast"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=bolt_of_aqshy_description_str}A fast moving flaming bolt that does medium damage on impact.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>boltofaqshy_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Sear"
                     Name="{=sear_label_str}Sear"
                     SpriteName="sear_icon"
                     CoolDown="20"
                     WindsOfMagicCost="10"
                     BaseMisCastChance="0.05"
                     Duration="4"
                     Radius="1"
                     AbilityType="Spell"
                     AbilityEffectType="Blast"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="sear"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="sear"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="7"
                     AbilityTargetType="WorldPosition"
                     CrosshairType="Wind"
                     MinDistance="1"
                     MaxDistance="2"
                     TargetCapturingRadius="2"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=sear_description_str}Blast the enemy with a short ranged searing flame attack that does medium damage.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>sear_burst</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CascadingFireCloak"
                     Name="{=cascading_fire_cloak_label_str}Cascading Fire-Cloak"
                     SpriteName="cascadingfirecloak_icon"
                     CoolDown="75"
                     WindsOfMagicCost="16"
                     BaseMisCastChance="0.05"
                     Duration="4"
                     Radius="1"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="20"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="sear"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     MinDistance="1"
                     MaxDistance="20"
                     TargetCapturingRadius="1"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=cascading_fire_cloak_description_str}The caster wraps himself in a cloak of magical flame that shields him from physical (20%) and fire (70%) damage for 30 seconds. The cloak also reflects a portion of damage taken in melee.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_fire_cloak_augment</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FlamingSwords"
                     Name="{=flaming_swords_label_str}Flaming Sword of Rhuin"
                     SpriteName="swordsofrhuin_icon"
                     CoolDown="90"
                     WindsOfMagicCost="18"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=flaming_swords_description_str}Buff the weapons of allies in a 15 meter radius with the Flaming trait for 45 seconds. The weapons burst into fire and burn every enemy they strike.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_flaming_sword_trait</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="BurningHead"
                     Name="{=burning_head_label_str}The Burning Head"
                     SpriteName="burninghead_icon"
                     CoolDown="70"
                     WindsOfMagicCost="45"
                     BaseMisCastChance="0.3"
                     Duration="3"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Wind"
                     BaseMovementSpeed="12"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="burning_skull"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="passing_fire"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Wind"
                     MinDistance="2"
                     MaxDistance="10"
                     TargetCapturingRadius="3"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=burning_head_description_str}Conjure a large burning skull that moves forward burning everything in its path.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>burning_head_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FlameStorm"
                     Name="{=flame_storm_label_str}Fire Tornado"
                     SpriteName="flamingstorm_icon"
                     CoolDown="70"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.3"
                     Duration="11"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Vortex"
                     BaseMovementSpeed="3"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="flamestorm"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="flamestorm"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="3"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="3"
                     MaxDistance="20"
                     TargetCapturingRadius="2"
                     MaxRandomDeviation="4"
                     ShouldRotateVisuals="true"
                     VisualsRotationVelocity="20"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfFire"
                     TooltipDescription="{=flame_storm_description_str}A randomly moving pillar of searing flame is summoned on the battlefield. Flesh and skin melt, while bones turn to ash.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>burning_head_tick</TriggeredEffect>
    </AbilityTemplate>
    <!-- Lore of Heavens -->
    <AbilityTemplate StringID="LightningBolt"
                     Name="{=lightning_bolt_label_str}Lightning Bolt"
                     SpriteName="lightningbolt_icon"
                     CoolDown="15"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="75"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="lightning_bolt"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="electric_buzz"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=lightning_bolt_description_str}Hurl an electric bolt at the enemy that does medium damage on impact.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>lightningbolt_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="WindBlast"
                     Name="{=wind_blast_label_str}Wind Blast"
                     SpriteName="wind_blast_icon"
                     CoolDown="30"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="4"
                     Radius="1"
                     AbilityType="Spell"
                     AbilityEffectType="Blast"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="wind_blast"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="dust_storm"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="14"
                     AbilityTargetType="WorldPosition"
                     CrosshairType="Wind"
                     MinDistance="1"
                     MaxDistance="2"
                     TargetCapturingRadius="4"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=wind_blast_description_str}A gust of wind knocks down victims and applies minor damage.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>windblast_burst</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ChainLightning"
                     Name="{=chain_lightning_label_str}Chain Lightning"
                     SpriteName="chainlightning_icon"
                     CoolDown="45"
                     WindsOfMagicCost="25"
                     BaseMisCastChance="0.3"
                     Duration="12"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Vortex"
                     BaseMovementSpeed="4"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="ball_lightning"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="electric_buzz"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="3"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="3"
                     MaxDistance="20"
                     TargetCapturingRadius="2"
                     MaxRandomDeviation="4"
                     ShouldRotateVisuals="true"
                     VisualsRotationVelocity="4"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=chain_lightning_description_str}A ball of lightning that zaps enemies in its area for medium damage.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>lightning_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="HarmonicConvergence"
                     Name="{=harmonic_convergence_label_str}Harmonic Convergence"
                     SpriteName="harmonicconvergence_icon"
                     CoolDown="90"
                     WindsOfMagicCost="24"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="5"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=harmonic_convergence_description_str}Divining favorable signs, the caster guides the minds of his fellow warriors. Allies in a 10 meter radius gain 50% physical resistance for 50 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>physical_resistance_50</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Blizzard"
                     Name="{=blizzard_label_str}Iceshard Blizzard"
                     SpriteName="iceshardblizzard_icon"
                     CoolDown="30"
                     WindsOfMagicCost="22"
                     BaseMisCastChance="0.5"
                     Duration="5"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="3"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="iceshard_blizzard"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="hailstorm"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="5"
                     MaxDistance="25"
                     TargetCapturingRadius="2"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=blizzard_description_str}Stormy clouds loom over the area showering those below with sharp shards of ice.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>blizzard_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CometOfCasandora"
                     Name="{=comet_of_casandora_label_str}Comet of Casandora"
                     SpriteName="comet_icon"
                     CoolDown="90"
                     WindsOfMagicCost="40"
                     BaseMisCastChance="0.3"
                     Duration="30"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="4"
                     TickInterval="0.5"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="comet_of_casandora"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="100"
                     CrosshairType="TargetedAOE"
                     AbilityTargetType="GroundAtPosition"
                     MinDistance="3"
                     MaxDistance="35"
                     TargetCapturingRadius="6"
                     MaxRandomDeviation="1"
                     ShouldRotateVisuals="true"
                     VisualsRotationVelocity="1"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=comet_of_casandora_description_str}A large meteor strikes the area killing all below in a giant shockwave.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>comet_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Thunderbolt"
                     Name="{=thunderbolt_label_str}Urannon's Thunderbolt"
                     SpriteName="thunderbolt_icon"
                     CoolDown="40"
                     WindsOfMagicCost="22"
                     BaseMisCastChance="0.5"
                     Duration="4"
                     Radius="1"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="0.3"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="3"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="thunderbolt"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="lightning_strike"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="5"
                     MaxDistance="25"
                     TargetCapturingRadius="2"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=thunderbolt_description_str}A single large bolt of lightning crashes down from the skies with a sizeable electric charge.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>thunderbolt_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CurseOfMidnightWind"
                     Name="{=curse_of_midnight_wind_label_str}Curse of the Midnight Wind"
                     SpriteName="curse_of_the_midnight_wind_icon"
                     CoolDown="60"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="5"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="curse_of_midnight_wind"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfHeavens"
                     TooltipDescription="{=curse_of_midnight_wind_description_str}The wizard speaks an ancient three-word hex, cursing the enemies and iposing a 40% penalty to movement and attack speed, as well as damage dealt.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>curse_of_midnight</TriggeredEffect>
    </AbilityTemplate>
    <!-- Lore of Light -->
    <AbilityTemplate StringID="ShemGaze"
                     Name="{=shem_gaze_label_str}Shem's Burning Gaze"
                     SpriteName="shemsburninggaze_icon"
                     CoolDown="20"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="50"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="10"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="shem_gaze"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fire_cast"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=shem_gaze_description_str}A bolt of cleansing energy fly from the caster's hands, searing evil wherever it strikes.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>shemgaze_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="HealingLight"
                     Name="{=healing_light_label_str}Healing Light"
                     SpriteName="healinglight_icon"
                     CoolDown="60"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Heal"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="3"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=healing_light_description_str}Allies in the area of effect regenerate health over time.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_healing_statuseffect</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="PhaProtection"
                     Name="{=pha_protection_label_str}Pha's Protection"
                     SpriteName="phasprotection_icon"
                     CoolDown="90"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="10"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=pha_protection_description_str}The wizard calls upon the beneficent Guardian of Light to protect his allies from harm. Grants 50% physical resistance for a duration 50 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>physical_resistance_50</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="DeathlyShards"
                     Name="{=deathly_shards_label_str}Deathly Shards"
                     SpriteName="deadlyshards_icon"
                     CoolDown="30"
                     WindsOfMagicCost="15"
                     BaseMisCastChance="0.05"
                     Duration="10"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="45"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="deathly_shards"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="shards_loop"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=deathly_shards_description_str}Sharp shards of light fly towards the target piercing through the toughest armor.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>shard_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="RadiancePillar"
                     Name="{=radiance_pillar_label_str}Pillar of Radiance"
                     SpriteName="pillarofradiance_icon"
                     CoolDown="60"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.5"
                     Duration="5"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="3"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="radiance_pillar"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="radiance_pillar"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="5"
                     MaxDistance="30"
                     TargetCapturingRadius="2"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=radiance_pillar_description_str}A large pillar of light applies high damage to those caught in the beam.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>pillar_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="RadiantGaze"
                     Name="{=radiant_gaze_label_str}Radiant Gaze"
                     SpriteName="radiantgaze_icon"
                     CoolDown="75"
                     WindsOfMagicCost="45"
                     BaseMisCastChance="0.05"
                     Duration="5"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Wind"
                     BaseMovementSpeed="9"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="radiant_gaze"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="windofdeath_whispers"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     CrosshairType="Wind"
                     AbilityTargetType="GroundAtPosition"
                     MinDistance="2"
                     MaxDistance="10"
                     TargetCapturingRadius="3"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=radiant_gaze_description_str}Columns of light rise from the ground in a path.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>wind_of_death_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="NetOfAmyntok"
                     Name="{=net_of_amyntok_label_str}Net of Amyntok"
                     SpriteName="netofamyntok_icon"
                     CoolDown="65"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="20"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="100"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=net_of_amyntok_description_str}The wizard conjures an arcane net in the selected area. Any foe caught in the spell is halted for 25 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>net_of_amyntok_hex</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="BironasTimewarp"
                     Name="{=bironas_timewarp_label_str}Birona's Timewarp"
                     SpriteName="bironastimewarp_icon"
                     CoolDown="60"
                     WindsOfMagicCost="15"
                     BaseMisCastChance="0.05"
                     Duration="15"
                     Radius="15"
                     AbilityType="Spell"
                     AbilityEffectType="TimeWarpEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="timewarp_ground"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="Self"
                     TargetCapturingRadius="15"
                     MaxDistance="20"
                     CrosshairType="Self"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfLight"
                     TooltipDescription="{=bironas_timewarp_description_str}Slows down time, while units around you move unhindered.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_bironas_timewarp</TriggeredEffect>
    </AbilityTemplate>
    <!-- Necromancy -->
    <AbilityTemplate StringID="NagashGaze"
                     Name="{=nagash_gaze_label_str}Gaze of Nagash"
                     SpriteName="gazeofnagash_icon"
                     CoolDown="12"
                     WindsOfMagicCost="7"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="50"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="10"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="nagash_gaze"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="evilbolt_cast"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=nagash_gaze_description_str}A bolt of Dhar flies from the caster's eyes, and what flesh it touches blackens and withers, peeling away completely to reveal bleached white bone beneath.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>nagashgaze_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="SummonSkeleton"
                     Name="{=summon_skeleton_label_str}Raise the Dead"
                     SpriteName="summonskeleton_icon"
                     CoolDown="30"
                     WindsOfMagicCost="15"
                     BaseMisCastChance="0.3"
                     Duration="4"
                     Radius="2.5"
                     AbilityType="Spell"
                     AbilityEffectType="Summoning"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="raise_dead"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="rocks_grinding"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="1"
                     MaxDistance="25"
                     TargetCapturingRadius="5"
                     SpellTier="2"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=summon_skeleton_description_str}Raises old bones from the ground for the Necromancer to command. Summon 25 Skeleton Warriors.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>summon_skeleton</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Shadowblood"
                     Name="{=shadowblood_label_str}Shadowblood"
                     SpriteName="shadowblood_icon"
                     CoolDown="15"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="300"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="SeekerMissile"
                     BaseMovementSpeed="40"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="shadowblood"
                     MaxDistance="120"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="evilbolt_cast"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="2"
                     CrosshairType="SingleTarget"
                     AbilityTargetType="EnemiesInAOE"
                     SpellTier="2"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=shadowblood_description_str}Shadowy orbs of blood follow the target staining the ground beneath every step of the way. Deals damage in a medium area.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>shadowblood_explosion</TriggeredEffect>
        <SeekerParameters Proportional="0.5"
                          Derivative="0"
                          DisableDistance="10" />
    </AbilityTemplate>
    <AbilityTemplate StringID="Morkharn"
                     Name="{=morkharn_label_str}Morkharn - Breath of Darkness"
                     SpriteName="breathofdarkness_icon"
                     CoolDown="60"
                     WindsOfMagicCost="12"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Heal"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=morkharn_description_str}The wizard draws tendrils of the Wind of Death to restore his minions and fill them with unholy vigour. Heals allies over time in a radius.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>breath_of_darkness</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="VanHelsDanseMacabre"
                     Name="{=vanhelsdansemacabre_label_str}Vanhel's Danse Macabre"
                     SpriteName="vanhels_dance_macabre_icon"
                     CoolDown="40"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="8"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=vanhelsdansemacabre_description_str}The animated dead are normally ungainly and cumbersome; this spell, however, gives them grace and speed - and makes them all the more deadly as a result.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_vanhelsdansemacabre</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="WindOfDeath"
                     Name="{=wind_of_death_label_str}Wind of Death"
                     SpriteName="windofdeath_icon"
                     CoolDown="65"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.3"
                     Duration="5"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Wind"
                     BaseMovementSpeed="9"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="wind_of_death_vfx"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="windofdeath_whispers"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     CrosshairType="Wind"
                     AbilityTargetType="GroundAtPosition"
                     MinDistance="2"
                     MaxDistance="10"
                     TargetCapturingRadius="3"
                     SpellTier="4"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=wind_of_death_description_str}Invoke the spirits of the dead to consume the souls of the living. Applies medium damage while moving along its path.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>wind_of_death_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="GraveCall"
                     Name="{=grave_call_label_str}Grave Call"
                     SpriteName="gravecall_icon"
                     CoolDown="60"
                     WindsOfMagicCost="25"
                     BaseMisCastChance="0.3"
                     Duration="4"
                     Radius="2.5"
                     AbilityType="Spell"
                     AbilityEffectType="Summoning"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="raise_dead"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="rocks_grinding"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="1"
                     MaxDistance="20"
                     TargetCapturingRadius="5"
                     SpellTier="4"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=grave_call_description_str}Drawing forth the dark energy coursing through the land, the necromancer searches long dead bodies to answer his summons. Raise 25 Grave Guard.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>summon_gravecall</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CurseOfYears"
                     Name="{=curse_of_years_label_str}Curse of Years"
                     SpriteName="curseofyears_icon"
                     CoolDown="60"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="15"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="1.9"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="Necromancy"
                     TooltipDescription="{=curse_of_years_description_str}The necromancer utters an ancient curse, which causes the foe to age at a horrific rate. Reduces enemy attack and movement speed by 50% for a duration of 30 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>curse_of_years_hex</TriggeredEffect>
    </AbilityTemplate>
    <!-- Dark Magic -->
    <AbilityTemplate StringID="Chillwind"
                     Name="{=chillwind_label_str}Chillwind"
                     SpriteName="chillwind_icon"
                     CoolDown="20"
                     WindsOfMagicCost="10"
                     BaseMisCastChance="0.05"
                     Duration="4"
                     Radius="1"
                     AbilityType="Spell"
                     AbilityEffectType="Blast"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="chillwind"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="dust_storm"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="12"
                     AbilityTargetType="WorldPosition"
                     CrosshairType="Wind"
                     MinDistance="2"
                     MaxDistance="3"
                     TargetCapturingRadius="4"
                     SpellTier="2"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=chillwind_description_str}The caster assails the enemy with a freezing gale.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>chillwind_burst</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="DoomBolt"
                     Name="{=doom_bolt_label_str}Doom Bolt"
                     SpriteName="doombolt_icon"
                     CoolDown="80"
                     WindsOfMagicCost="40"
                     BaseMisCastChance="0.3"
                     Duration="30"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="4"
                     TickInterval="0.5"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="doom_bolt_prefab"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="100"
                     CrosshairType="TargetedAOE"
                     AbilityTargetType="GroundAtPosition"
                     MinDistance="3"
                     MaxDistance="30"
                     TargetCapturingRadius="6"
                     MaxRandomDeviation="1"
                     ShouldRotateVisuals="true"
                     VisualsRotationVelocity="1"
                     SpellTier="4"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=doom_bolt_description_str}The wizard rips open the sky and calls down a bolt burning with dark flames on the foe.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>doom_bolt_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="DeathSpasm"
                     Name="{=death_spasm_label_str}Death Spasm"
                     SpriteName="deathspasm_icon"
                     CoolDown="15"
                     WindsOfMagicCost="12"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.5"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="45"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="doombolt"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="evilbolt_cast"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=death_spasm_description_str}A crackling black bolt of energy that explodes in a fireball and sets victims on fire.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>deathspasm_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="SoulRain"
                     Name="{=soul_rain_label_str}Soul Rain"
                     SpriteName="soulrain_icon"
                     CoolDown="50"
                     WindsOfMagicCost="22"
                     BaseMisCastChance="0.5"
                     Duration="5"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="3"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="souldrain"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="lightning_strike"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="5"
                     MaxDistance="25"
                     TargetCapturingRadius="3"
                     SpellTier="3"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=soul_rain_description_str}A black cloud looms over the targeted area striking those below with arcane lightning bolts.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>soulrain_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="PowerOfDarkness"
                     Name="{=power_of_darkness_label_str}Power of Darkness"
                     SpriteName="powerofdarkness_icon"
                     CoolDown="70"
                     WindsOfMagicCost="18"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="6"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=power_of_darkness_description_str}The caster draws unstable power from the Realm of Chaos to empower his allies. Grants 20% physical bonus damage for a duration of 35 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>power_of_darkness_bonus</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ScreamingSkull"
                     Name="{=screaming_skull_label_str}Screaming Skull"
                     SpriteName="screamingskull_icon"
                     CoolDown="65"
                     WindsOfMagicCost="30"
                     BaseMisCastChance="0.3"
                     Duration="3"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Wind"
                     BaseMovementSpeed="12"
                     TickInterval="0.75"
                     TriggerType="EveryTick"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="screaming_skull_vfx"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="scream"
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     Offset="10"
                     CrosshairType="Wind"
                     AbilityTargetType="GroundAtPosition"
                     MinDistance="2"
                     MaxDistance="10"
                     TargetCapturingRadius="4"
                     SpellTier="3"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=screaming_skull_description_str}Conjures a screaming skull dripping with acid that moves forward damaging everything in its path.">
        <LightColorRGB x="52"
                       y="235"
                       z="88"
                       w="-1">
            <AsVec2>
                <x>52</x>
                <y>235</y>
                <z>88</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>screaming_skull_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="BlackHorror"
                     Name="{=black_horror_label_str}Arnzipal's Black Horror"
                     SpriteName="arzipalsblackhorror_icon"
                     CoolDown="70"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.3"
                     Duration="8"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Vortex"
                     BaseMovementSpeed="2"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="black_horror"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="windofdeath_whispers"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="3"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="3"
                     MaxDistance="20"
                     TargetCapturingRadius="3"
                     MaxRandomDeviation="2"
                     ShouldRotateVisuals="false"
                     VisualsRotationVelocity="0"
                     SpellTier="4"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=black_horror_description_str}The caster tears down the walls between realities, and a black cloud of roiling energy sweeps across the battlefield. As the darkness travels, slimy tentacles lash out from its depths, draggin unfortunate victims screaming to an unknown fate.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>witheringwave_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="SoulStealer"
                     Name="{=soul_stealer_label_str}Soul Stealer"
                     SpriteName="soulstealer_icon"
                     CoolDown="80"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="4"
                     BelongsToLoreID="DarkMagic"
                     TooltipDescription="{=soul_stealer_description_str}Enemies affected experience a soul draining damage over time effect, causing them to take 12 damage every second for a duration of 12 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>soulstealer</TriggeredEffect>
    </AbilityTemplate>
    <!-- High Magic -->
    <AbilityTemplate StringID="Apotheosis"
                     Name="{=apotheosis_label_str}Apotheosis"
                     SpriteName="apoteosis_icon"
                     CoolDown="25"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="12"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="apotheosis_ground"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="25"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=apotheosis_description_str}The wizard infuses and invigorates his allies with pure magical energies.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apotheosis_heal</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FieryConvocation"
                     Name="{=fiery_convocation_label_str}Fiery Convocation"
                     SpriteName="fiery_convocation_icon"
                     CoolDown="70"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.05"
                     Duration="5"
                     Radius="8"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="snow_king_decree_explosion"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="30"
                     CrosshairType="SingleTarget"
                     SpellTier="4"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=fiery_convocation_description_str}Utter one secret word, and your foes shall burst with fire and flame. Deals large damage as well as damage over time in the area.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>fiery_convocation</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="SoulQuench"
                     Name="{=soul_quench_label_str}Soul Quench"
                     SpriteName="soul_quench_icon"
                     CoolDown="30"
                     WindsOfMagicCost="15"
                     BaseMisCastChance="0.05"
                     Duration="100"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="25"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="15"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="soul_quench"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fireball"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=soul_quench_description_str}A brilliant sphere of light emerges from the wizard's fingertips. Enemies affected suffer medium damage.">
        <LightColorRGB x="245"
                       y="247"
                       z="129"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>soul_quench_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CourageOfAenarion"
                     Name="{=courage_of_aenarion_label_str}Courage of Aenarion"
                     SpriteName="courage_of_aenarion_icon"
                     CoolDown="60"
                     WindsOfMagicCost="18"
                     BaseMisCastChance="0.05"
                     Duration="10"
                     Radius="6"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="7"
                     MaxDistance="2"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=courage_of_aenarion_description_str}The Elf mage channels all eight Winds of Magic and invokes the name of Aenarion, the first of Phoenix Kings. Allies around the wizard strike with greater frequency.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>courage_of_aenarion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CurseOfArrowAttraction"
                     Name="{=curse_of_arrowattraction_label_str}Curse of Arrow Attraction"
                     SpriteName="curse_of_arrowattraction_icon"
                     CoolDown="30"
                     WindsOfMagicCost="13"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="20"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="1.9"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="30"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=curse_of_arrowattraction_description_str}The Mage places a curse in the area - the air around enemies distorts and guides allied projectiles with astounding accuracy.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>curse_of_arrows</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Tempest"
                     Name="{=tempest_label_str}Tempest"
                     SpriteName="tempest_icon"
                     CoolDown="60"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.3"
                     Duration="8"
                     Radius="2"
                     AbilityType="Spell"
                     AbilityEffectType="Vortex"
                     BaseMovementSpeed="2"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="tempest"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="windofdeath_whispers"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="5"
                     MaxDistance="20"
                     TargetCapturingRadius="6"
                     MaxRandomDeviation="2"
                     ShouldRotateVisuals="true"
                     VisualsRotationVelocity="30"
                     SpellTier="4"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=tempest_description_str}Conjures a brilliant vortex of shimmering light containing all eight colors of Winds of Magic.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>witheringwave_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ShieldOfSaphery"
                     Name="{=shield_of_saphery_label_str}Shield of Saphery"
                     SpriteName="shield_of_saphery_icon"
                     CoolDown="30"
                     WindsOfMagicCost="17"
                     BaseMisCastChance="0.05"
                     Duration="45"
                     Radius="8"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="ethereal_dome_large"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="AlliesInAOE"
                     CrosshairType="TargetedAOE"
                     MaxDistance="40"
                     TargetCapturingRadius="4"
                     SpellTier="3"
                     BelongsToLoreID="HighMagic"
                     TooltipDescription="{=shield_of_saphery_description_str}The caster summons an ethereal dome to protect his allies from ranged fire.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>aerial_shield</TriggeredEffect>
    </AbilityTemplate>
    <!-- Lore of Life-->
    <AbilityTemplate StringID="DwellersBelow"
                     Name="{=dwellers_below_label_str}Dwelllers Below"
                     SpriteName="dwellersbelow_icon"
                     CoolDown="70"
                     WindsOfMagicCost="35"
                     BaseMisCastChance="0.05"
                     Duration="15"
                     Radius="20"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="1.9"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="dwellers_below_ground"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay="dwellers_below"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     AbilityTargetType="GroundAtPosition"
                     TargetCapturingRadius="5"
                     MaxDistance="25"
                     CrosshairType="TargetedAOE"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=dwellers_below_description_str}Tendrils emerge from the ground reaching out and attacking enemies in the area. Knocks down, dismounts and slows (80%) enemies, while dealing 10 damage per second for 15 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>dwellers_below_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="TheGreenEye"
                     Name="{=the_green_eye_label_str}The Green Eye"
                     SpriteName="greeneye_icon"
                     CoolDown="12"
                     WindsOfMagicCost="5"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Projectile"
                     BaseMovementSpeed="100"
                     TickInterval="-1"
                     TriggerType="OnStop"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="green_eye"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="rocks_grinding"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=the_green_eye_description_str}Caster merges their eyes, firing a single fast, penetrating projectile in the aimed direction.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>green_eye_end</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Barkskin"
                     Name="{=barkskin_label_str}Barkskin"
                     SpriteName="barkskin_icon"
                     CoolDown="100"
                     WindsOfMagicCost="25"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="7"
                     MaxDistance="0.5"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=barkskin_description_str}Hardens the skin of targets, making them tougher, providing 50% physical resistance for 80 secouds">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>bark_skin</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="SummerHeat"
                     Name="{=summer_heat_label_str}Summer Heat"
                     SpriteName="summerheat_icon"
                     CoolDown="40"
                     WindsOfMagicCost="12"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="20"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="1.9"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="6"
                     MaxDistance="25"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=summer_heat_description_str}A small area radiates unbearable heat, causing fatigue, impairing movement speed by 50%, and making the enemies slightly vulnerable to fire damage for 15 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>summer_heat</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Regrowth"
                     Name="{=regrowth_label_str}Regrowth"
                     SpriteName="regrowth_icon"
                     CoolDown="25"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=regrowth_description_str}Invoking the name of Duthandor, lord of the ancient wildshrines, the wizard mends wounds and broken bones of his comrades. Allies in a 3 meter radius gain 5 HP per second for 12 seconds.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>regrowth_heal</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="DrainLife"
                     Name="{=drain_life_label_str}Drain Life"
                     SpriteName="drainlife_icon"
                     CoolDown="35"
                     WindsOfMagicCost="18"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="3"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=drain_life_description_str}Sucks the moisture and fluids from the foe, dealing 5 damage per second for total duration of 10 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>drain_life</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="StormOfRenewal"
                     Name="{=storm_of_renewal_label_str}Storm of Renewal"
                     SpriteName="stormofrenewal_icon"
                     CoolDown="50"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="12"
                     Radius="5"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="true"
                     LightIntensity="300"
                     LightRadius="3"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=storm_of_renewal_description_str}Raw energy flows across the battlefield, renewing lost vigour and mending wounds. Heals 30 hitpoints flat, and 10 hitpoints of all friendly targets inside the area for 12 seconds.">
        <LightColorRGB x="50"
                       y="255"
                       z="50"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>storm_of_renewal</TriggeredEffect>
    </AbilityTemplate>
	<AbilityTemplate StringID="ShieldOfThorns"
                     Name="{=barkskin_label_str}Shield of Thorns"
                     SpriteName="shield_of_thorns_icon"
                     CoolDown="70"
                     WindsOfMagicCost="16"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="7"
                     MaxDistance="10"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfLife"
                     TooltipDescription="{=shieldofthorns_description_str}Prickly gorse protects allies in the radius. Reflects 25% of incoming melee damage.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>shield_of_thorns</TriggeredEffect>
    </AbilityTemplate>
    <!-- Lore of Beasts-->
    <AbilityTemplate StringID="AmberSpear"
                     Name="{=amber_spear_label_str}Amber Spear"
                     SpriteName="amberspear_icon"
                     CoolDown="4"
                     WindsOfMagicCost="3"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Projectile"
                     BaseMovementSpeed="90"
                     TickInterval="1"
                     TriggerType="OnStop"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="amber_spear"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="rocks_grinding"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=amber_spear_description_str}The caster throws a javelin like amber spear in the targeted direction that penetrates multiple targets.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>amber_spear</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="TanglingThorn"
                     Name="{=tangling_thorn_label_str}Tangling Thorn"
                     SpriteName="tanglingthorn_icon"
                     CoolDown="40"
                     WindsOfMagicCost="12"
                     BaseMisCastChance="0.05"
                     Duration="10"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="5"
                     AbilityTargetType="GroundAtPosition"
                     TargetCapturingRadius="3"
                     MaxDistance="15"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=tangling_thorn_description_str}Roots erupt from the ground preventing all enemies from moving for a duration of 10 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>tangling_thorn</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="PannsImpenetrablePelt"
                     Name="{=panns_impenetrable_pelt_label_str}Pann's Impenetrable Pelt"
                     SpriteName="pannsimpenetrablepelt_icon"
                     CoolDown="60"
                     WindsOfMagicCost="16"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="1"
                     SpellTier="2"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=panns_impenetrable_pelt_description_str}A magical skin surrounds the wizard, protecting him from physical damage (40%) for a duration of 40 seconds.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>pans_pelt</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="HailStorm"
                     Name="{=hail_storm_label_str}Hail Storm"
                     SpriteName="hailstorm_icon"
                     CoolDown="45"
                     WindsOfMagicCost="18"
                     BaseMisCastChance="0.5"
                     Duration="5"
                     Radius="6"
                     AbilityType="Spell"
                     AbilityEffectType="Bombardment"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="3"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="hail_storm"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="lightning_strike"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="5"
                     MaxDistance="25"
                     TargetCapturingRadius="3"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=hail_storm_description_str}A storm of stones falls down on the foe, dealing physical damage in a radius.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>phys_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FlockOfDoom"
                     Name="{=flock_of_doom_label_str}Flock Of Doom"
                     SpriteName="flockofdoom_icon"
                     CoolDown="35"
                     WindsOfMagicCost="26"
                     BaseMisCastChance="0.05"
                     Duration="10"
                     Radius="7.5"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="10"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=flock_of_doom_description_str}Summons a nightmare of crows in the targeted area. Affected enemies suffer light damage over time and their physical resistance is reduced by 20% for 10 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>flock_of_doom</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="TheBeastUnleashed"
                     Name="{=the_beast_unleashed_label_str}The Beast Unleashed"
                     SpriteName="beastunleashed_icon"
                     CoolDown="60"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="12"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="15"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=the_beast_unleashed_description_str}Unleashes the primal savagery of the caster's allies, driving them into a frenzy. Grants a 25% physical damage and attack speed bonus for a duration of 60 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>beast_unleashed</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="CurseOfAnraheir"
                     Name="{=curse_of_anraheir_label_str}Curse Of Anraheir"
                     SpriteName="curseofanraheir_icon"
                     CoolDown="90"
                     WindsOfMagicCost="32"
                     BaseMisCastChance="0.05"
                     Duration="15"
                     Radius="10"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="10"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfBeasts"
                     TooltipDescription="{=curse_of_anraheir_description_str}Woodland sprites hinder all enemies caught in the area for 15 seconds, reducing movement (35%) and attack speed (50%) as well as causing 3 damage per second.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>curse_of_anraheir</TriggeredEffect>
    </AbilityTemplate>
    <!-- Lore of Metal-->
    <AbilityTemplate StringID="QuicksilverSwords"
                     Name="{=quicksilver_swords_label_str}Quicksilver Swords"
                     SpriteName="quicksilversword_icon"
                     CoolDown="70"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="7"
                     MaxDistance="25"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfMetal"
                     TooltipDescription="{=quicksilver_swords_label_str}Infuses weapons of allies with gleaming energies - Even a rusty sword turns deadly with the help of a Metal Wizard. Friendly units deal 20% magic and fire damage for a duration of 60 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_quicksilver_sword_trait</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="GleamingArrow"
                     Name="{=gleaming_arrow_label_str}Gleaming Arrow"
                     SpriteName="gleamingarrow_icon"
                     CoolDown="10"
                     WindsOfMagicCost="4"
                     BaseMisCastChance="0.05"
                     Duration="300"
                     Radius="0.5"
                     AbilityType="Spell"
                     AbilityEffectType="SeekerMissile"
                     BaseMovementSpeed="75"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="gleaming_arrow"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="dart"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_stone"
                     TriggeredEffectID="dart_explosion"
                     SpellTier="1"
                     BelongsToLoreID="LoreOfMetal"
                     MaxDistance="100"
                     CrosshairType="SingleTarget"
                     AbilityTargetType="SingleEnemy"
                     TooltipDescription="{=gleaming_arrow_description_str}The Gold Wizard fires a projectile of golden energy out of his forehead. The missile homes in on the target and deals 100 physical damage.">
        <LightColorRGB x="255"
                       y="200"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>204</x>
                <y>255</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>gleaming_arrow_explosion</TriggeredEffect>
        <SeekerParameters Proportional="0.5"
                          Derivative="0"
                          DisableDistance="10" />
    </AbilityTemplate>
    <AbilityTemplate StringID="MeteroicIronclad"
                     Name="{=meteoric_ironclad_label_str}Meteoric Ironclad"
                     SpriteName="meteriorironclad_icon"
                     CoolDown="60"
                     WindsOfMagicCost="20"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Spell"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="10"
                     MaxDistance="0.5"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfMetal"
                     TooltipDescription="{=meteoric_ironclad_description_str}Allies are protected by an enchantment of iron and silver. Provides 60% physical resistance for a duration of 45 seconds">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>meteoric_ironclad</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="PlagueOfRust"
                     Name="{=plague_of_rust_label_str}Plague Of Rust"
                     SpriteName="plagueofrust_icon"
                     CoolDown="90"
                     WindsOfMagicCost="16"
                     BaseMisCastChance="0.05"
                     Duration="2"
                     Radius="20"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="8"
                     MaxDistance="18"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfMetal"
                     TooltipDescription="{=plague_of_rust_description_str}At a flick of the caster's wrist, the armor of foes is consumed by rust. Imposes a 70% physical resistance penalty for a duration of 120 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>plague_of_rust</TriggeredEffect>
    </AbilityTemplate>
    <!-- <AbilityTemplate StringID="SearingDoom" 
      Name="{=wind_blast_label_str}Wind Blast" 
      SpriteName="wind_blast_icon" 
      CoolDown="20" 
      WindsOfMagicCost="8" 
      BaseMisCastChance="0.05" 
      Duration="4" 
      Radius="1" 
      AbilityType="Spell" 
      AbilityEffectType="Blast" 
      BaseMovementSpeed="0" 
      TickInterval="-1" 
      TriggerType="TickOnce" 
      HasLight="false" 
      LightIntensity="50" 
      LightRadius="10" 
      LightFlickeringMagnitude="0" 
      LightFlickeringInterval="0" 
      ShadowCastEnabled="false" 
      ParticleEffectPrefab="wind_blast" 
      ParticleEffectSizeModifier="1" 
      SoundEffectToPlay="dust_storm" 
      ShouldSoundLoopOverDuration="false"
      CastType="WindUp" 
      CastTime="0.5" 
      AnimationActionName="act_release_heavy_thrown" 
      Offset="5"
      AbilityTargetType="WorldPosition"
      CrosshairType="Wind"
      MinDistance="1"
      MaxDistance="2"
      TargetCapturingRadius="2"
      SpellTier="2"
      BelongsToLoreID="LoreOfMetal"
      TooltipDescription="{=wind_blast_description_str}A gust of wind knocks down victims and applies minor damage.">
		<LightColorRGB x="255" y="170" z="0" w="-1">
			<AsVec2>
				<x>255</x>
				<y>170</y>
			</AsVec2>
		</LightColorRGB>
		<TriggeredEffect>windblast_burst</TriggeredEffect>
	</AbilityTemplate> -->
    <AbilityTemplate StringID="GehennasGoldenGlobe"
                     Name="{=gehennas_golden_globe_label_str}Gehennas Golden Globe"
                     SpriteName="gehennasgoldenglobe_icon"
                     CoolDown="20"
                     WindsOfMagicCost="12"
                     BaseMisCastChance="0.05"
                     Duration="100"
                     Radius="0.4"
                     AbilityType="Spell"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="25"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="15"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="gehennas_golden_globe"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fireball"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     BelongsToLoreID="LoreOfMetal"
                     TooltipDescription="{=gehennas_golden_globe_description_str}A glittering globe of energy engulfs and burns foes alive. Those unfortunate enough to survive are also slowed down for a short duration.">
        <LightColorRGB x="245"
                       y="247"
                       z="129"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>golden_globe_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FinalTransmutation"
                     Name="{=final_transmutation_label_str}Final Transmutation"
                     SpriteName="finaltransmutation_icon"
                     CoolDown="100"
                     WindsOfMagicCost="45"
                     BaseMisCastChance="0.05"
                     Duration="1"
                     Radius="10"
                     AbilityType="Spell"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="final_transmutation_ground"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="7"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfMetal"
                     TooltipDescription="{=final_transmutation_description_str}By weaving the brilliant Wind of Chamon, the wizard attempts to transmute his foes into statues of pure gold. Deals continuous damage and greatly slows down enemies.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>final_transmutation</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="GehennasGoldenHounds"
                     Name="{=gehennas_golden_hounds_label_str}Gehennas Golden Hounds"
                     SpriteName="gehennasgoldenhounds_icon"
                     CoolDown="60"
                     WindsOfMagicCost="30"
                     BaseMisCastChance="0.3"
                     Duration="20"
                     Radius="6"
                     AbilityType="Spell"
                     AbilityEffectType="Vortex"
                     BaseMovementSpeed="1"
                     TickInterval="0.5"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="gehennas_golden_hound_ground"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="windofdeath_whispers"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.1"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     MinDistance="3"
                     MaxDistance="20"
                     TargetCapturingRadius="6"
                     MaxRandomDeviation="0.1"
                     ShouldRotateVisuals="false"
                     VisualsRotationVelocity="10"
                     SpellTier="4"
                     BelongsToLoreID="LoreOfMetal"
                     TooltipDescription="{=gehennas_golden_hounds_description_str}The wizard calls forth a pair of massive golden hounds that travel in a circular motion.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>golden_hounds_tick</TriggeredEffect>
    </AbilityTemplate>
    <!-- Artillery -->
    <AbilityTemplate StringID="MortarSpawner"
                     Name="{=mortar_spawner_label_str}Place Mortar"
                     SpriteName="placeartillery_icon"
                     CoolDown="1"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.0"
                     Duration="1"
                     Radius="2"
                     AbilityType="ItemBound"
                     AbilityEffectType="ArtilleryPlacement"
                     BaseMovementSpeed="0"
                     TickInterval="0.2"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Pointer"
                     MinDistance="1"
                     MaxDistance="30"
                     TargetCapturingRadius="2.5">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>place_mortar</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="GreatCannonSpawner"
                     Name="{=great_cannon_spawner_label_str}Place Great Cannon"
                     SpriteName="placeartillery_icon"
                     CoolDown="1"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.0"
                     Duration="1"
                     Radius="2"
                     AbilityType="ItemBound"
                     AbilityEffectType="ArtilleryPlacement"
                     BaseMovementSpeed="0"
                     TickInterval="0.2"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Pointer"
                     MinDistance="1"
                     MaxDistance="30"
                     TargetCapturingRadius="2.5">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>place_greatcannon</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FieldTrebuchetSpawner"
                     Name="{=field_trebuchet_spawner_label_str}Place Field Trebuchet"
                     SpriteName="placeartillery_icon"
                     CoolDown="1"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.0"
                     Duration="1"
                     Radius="2"
                     AbilityType="ItemBound"
                     AbilityEffectType="ArtilleryPlacement"
                     BaseMovementSpeed="0"
                     TickInterval="0.2"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Pointer"
                     MinDistance="1"
                     MaxDistance="30"
                     TargetCapturingRadius="2.5">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>place_fieldtrebuchet</TriggeredEffect>
    </AbilityTemplate>
    <!-- Career Abilities -->
    <AbilityTemplate StringID="ShadowStep"
                     Name="{=shadowstep_label_str}Mist Form"
                     SpriteName="mistform_icon"
                     CoolDown="5"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.3"
                     Duration="5"
                     Radius="2"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0.6"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="bats_vfx"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="bats_loop"
                     ShouldSoundLoopOverDuration="false"
                     AbilityTargetType="Self"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="none"
                     Offset="10"
                     CrosshairType="Self"
                     MinDistance="1"
                     MaxDistance="1"
                     TargetCapturingRadius="1">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_mistwalk</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="RighteousFury"
                     Name="{=righteous_fury_label_str}Righteous Fury"
                     SpriteName="righteousfury_icon"
                     CoolDown="15"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="8"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="AlliesInAOE"
                     CrosshairType="Self"
                     TargetCapturingRadius="4">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_righteous_fury</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Accusation"
                     Name="{=accusation_str}Accusation"
                     SpriteName="accusation_icon"
                     CoolDown="5"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="5"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="0.5"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     MaxDistance="150"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     AbilityTargetType="SingleEnemy"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="none"
                     Offset="0"
                     CrosshairType="SingleTarget"
                     TargetCapturingRadius="4">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_accusation</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="RedFury"
                     Name="{=red_fury_label_str}Red Fury"
                     SpriteName="redfury_icon"
                     CoolDown="10"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="6"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName=""
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="4">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_red_fury</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="LetThemHaveIt"
                     Name="{=red_fury_label_str}Let Them Have It"
                     SpriteName="redfury_icon"
                     CoolDown="15"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="15"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName=""
                     Offset="0"
                     AbilityTargetType="Self"
                     TargetCapturingRadius="4">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_let_them_have_it</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="AxeOfUlric"
                     Name="{=red_fury_label_str}Axe of Ulric"
                     SpriteName="axe_of_ulric_icon"
                     CoolDown="15"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="0.6"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="2"
                     TickInterval="-1"
                     TriggerType="OnStop"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_slam_twohanded"
                     Offset="1"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Self"
                     TargetCapturingRadius="2">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>ulric_smash</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="KnightlyCharge"
                     Name="{=knightly_charge_label_str}Knightly Charge"
                     SpriteName="knightlycharge_icon"
                     CoolDown="60"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="6"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName=""
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="4">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_knightly_charge</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="GreaterHarbinger"
                     Name="Greater Harbinger"
                     SpriteName="theharbinger_icon"
                     CoolDown="5"
                     BaseMisCastChance="0.3"
                     Duration="9999"
                     Radius="2"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="None"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Pointer"
                     MinDistance="1"
                     MaxDistance="60"
                     TargetCapturingRadius="1"
                     SpellTier="2"
                     TooltipDescription="Summon a champion on the target location.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>summon_champion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="MindControl"
                     Name="{=mindcontrol_label_str}Mind Control"
                     SpriteName="fellfangcontrol_icon"
                     CoolDown="40"
                     WindsOfMagicCost="8"
                     BaseMisCastChance="0.05"
                     Duration="300"
                     Radius="5"
                     AbilityType="CareerAbility"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="15"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="50"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     MaxDistance="30"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="evilbolt_cast"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.0"
                     TargetCapturingRadius="5"
                     CrosshairType="TargetedAOE"
                     AbilityTargetType="EnemiesInAOE"
                     SpellTier="2"
                     TooltipDescription="{=shadowblood_description_str}Shadowy orbs of blood fly towards the target staining the ground beneath every step of the way.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_mindcontrol</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="BlastOfAgony"
                     Name="Blast of Agony"
                     SpriteName="blastofagony_icon"
                     CoolDown="10"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.4"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="35"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     MaxDistance="120"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="blast_of_agony_prefab"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fireball"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     TooltipDescription="{=fireball_description_str}Launch a devastating blast of agony.">
        <LightColorRGB x="177"
                       y="0"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_blastofagony</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ArcaneConduit"
                     Name="{=red_fury_label_str}Arcane Conduit"
                     SpriteName="arcaneconduit_icon"
                     CoolDown="120"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0"
                     Duration="6"
                     Radius="1"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName=""
                     Offset="0"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     TargetCapturingRadius="4">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_arcaneconduit</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ArrowOfKurnous"
                     Name="Arrow of Kurnous"
                     SpriteName="arrowofkurnous_icon"
                     CoolDown="10"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="8"
                     Radius="0.4"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="80"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="50"
                     LightRadius="10"
                     MaxDistance="120"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="arrow_kurnous"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fireball"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_bow"
                     Offset="0.5"
                     CrosshairType="SingleTarget"
                     AbilityTargetType="EnemiesInAOE"
                     SpellTier="3"
                     TooltipDescription="{=fireball_description_str}Launch a devastating homing arrow.">
        <LightColorRGB x="52"
                       y="235"
                       z="88"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_arrow_of_kurnous</TriggeredEffect>
        <SeekerParameters Proportional="0.5"
                          Derivative="1"
                          DisableDistance="2" />
    </AbilityTemplate>
    <AbilityTemplate StringID="WrathOfTheWood"
                     Name="Wrath of the Wood"
                     SpriteName="wraith_of_the_forest_icon"
                     CoolDown="40"
                     BaseMisCastChance="0.3"
                     Duration="0"
                     Radius="2"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="Self"
                     CrosshairType="Self"
                     MinDistance="1"
                     MaxDistance="60"
                     TargetCapturingRadius="1"
                     SpellTier="2"
                     TooltipDescription="Summon dryads on the target location.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>summon_dryads</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="FeyPaths"
                     Name="Fey Paths"
                     SpriteName="feypaths_icon"
                     CoolDown="5"
                     BaseMisCastChance="0.3"
                     Duration="2"
                     Radius="2"
                     AbilityType="CareerAbility"
                     AbilityEffectType="CareerAbilityEffect"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="rocks_grinding"
                     ShouldSoundLoopOverDuration="true"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="10"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="Pointer"
                     MinDistance="1"
                     MaxDistance="60"
                     TargetCapturingRadius="1"
                     SpellTier="2"
                     TooltipDescription="The Damsel wanders on the fey paths. Instantly teleports the player to the targeted ground position. Charges with dealt or healed damage by magic.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
    </AbilityTemplate>
    <!-- Shallya Prayers (NPC ONLY) -->
    <AbilityTemplate StringID="BlessingOfShallya"
                     Name="{=regrowth_label_str}Regrowth"
                     SpriteName="regrowth_icon"
                     CoolDown="60"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="ShallyaPrayers"
                     TooltipDescription="{=regrowth_description_str}Invoking the name of Duthandor, lord of the ancient wildshrines, the wizard mends wounds and broken bones of his comrades. Allies in a 3 meter radius gain 5 HP per second for 12 seconds.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>regrowth_heal</TriggeredEffect>
    </AbilityTemplate>
    <!-- Sigmarite Prayers -->
    <AbilityTemplate StringID="CometOfSigmar"
                     Name="{=comet_of_sigmar_label_str}Comet of Sigmar"
                     SpriteName="comet_of_sigmar_icon"
                     CoolDown="30"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="3"
                     Radius="0.4"
                     AbilityType="Prayer"
                     AbilityEffectType="Missile"
                     BaseMovementSpeed="35"
                     TickInterval="-1"
                     TriggerType="OnCollision"
                     HasLight="true"
                     LightIntensity="15"
                     LightRadius="10"
                     LightFlickeringMagnitude="3"
                     LightFlickeringInterval="0.7"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="comet_of_sigmar"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="fireball"
                     ShouldSoundLoopOverDuration="true"
                     CastType="WindUp"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     CrosshairType="Missile"
                     AbilityTargetType="WorldPosition"
                     SpellTier="3"
                     BelongsToLoreID="SigmaritePrayers"
                     TooltipDescription="{=comet_of_sigmar_description_str}You throw a fiery missile that takes the form of Sigmar’s famed twin-tailed comet. A comet of Sigmar is a holy missile dealing 30 damage. It can knock units down, or throw them off horse. Only affects enemies">
        <LightColorRGB x="245"
                       y="247"
                       z="129"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>comet_of_sigmar_explosion</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="HealingHand"
                     Name="{=healing_hand_label_str}Healing Hand"
                     SpriteName="healinghand_icon"
                     DoNotAlignParticleEffectPrefab="true"
                     CoolDown="50"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.3"
                     Duration="9"
                     Radius="0.1"
                     AbilityType="Prayer"
                     AbilityEffectType="Heal"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="10"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="healing_hand_ground"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay="empire_heal"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="GroundAtPosition"
                     CrosshairType="TargetedAOE"
                     TargetCapturingRadius="5"
					 TooltipDescription="{=healing_hand_description_str}Allies in a 5 meter radius gain 5 Healthpoints every 2 seconds  for 9 seconds.">
                     BelongsToLoreID="SigmaritePrayers">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>healing_hand_tick</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ArmourOfRighteousness"
                     Name="{=armor_of_righteousness_label_str}Armour of Righteousness"
                     SpriteName="armorofrighteousness_icon"
                     CoolDown="70"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="0.5"
                     CrosshairType="TargetedAOE"
                     SpellTier="1"
                     BelongsToLoreID="SigmaritePrayers"
                     TooltipDescription="{=armor_of_righteousness_description_str}Allies in a 3 meter radius gain 35% physical resistance for 8 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>armour_of_the_righteous</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="Vanquish"
                     Name="{=vanquish_label_str}Vanquish"
                     SpriteName="vanquish_icon"
                     CoolDown="100"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="0"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="5"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="SigmaritePrayers"
                     TooltipDescription="{=vanquish_description_str}Buff the weapons of allies in a 5 meter radius with the Flaming trait for 20 seconds. The weapons burst into fire and burn every enemy they strike.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>apply_holy_sword_trait</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="UlricsGift"
                     Name="{=armor_of_righteousness_label_str}Ulric's Gift"
                     SpriteName="ulrics_gift_icon"
                     CoolDown="70"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="Self"
                     TargetCapturingRadius="4"
                     MaxDistance="0.5"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="UlricanPrayers"
                     TooltipDescription="{=armor_of_righteousness_description_str}The wolf-priest lets out a mighty howl and dedicates the kill to Ulric. Gain +30% movement and attack speed for 25 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>ulrics_gift</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="HeartOfTheWolf"
                     Name="{=armor_of_righteousness_label_str}Heart of The Wolf"
                     SpriteName="heart_of_wolf_icon"
                     CoolDown="70"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="10"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="0.5"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="UlricanPrayers"
                     TooltipDescription="{=armor_of_righteousness_description_str}The priest's allies are driven into a zealous frenzy. Friendly troops in a 10 meter radius gain +35% physical damage as well as Unbreakable and Unstoppable for 20 seconds.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>heart_of_the_wolf</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="IceStorm"
                     Name="{=armor_of_righteousness_label_str}Ice Storm"
                     SpriteName="ice_storm_icon"
                     CoolDown="70"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="3"
                     AbilityType="Prayer"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab=""
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="0.5"
                     CrosshairType="TargetedAOE"
                     SpellTier="3"
                     BelongsToLoreID="UlricanPrayers"
                     TooltipDescription="{=armor_of_righteousness_description_str}The wolf-priest summons an icy hailstorm within an area. Enemies suffer a -50% movement and attack speed penalty as well as damage over time.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>ice_storm</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="SnowKingDecree"
                     Name="{=armor_of_righteousness_label_str}The Snow King's Decree"
                     SpriteName="snow_kings_decree_icon"
                     CoolDown="70"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="5"
                     Radius="8"
                     AbilityType="Prayer"
                     AbilityEffectType="Hex"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="true"
                     ParticleEffectPrefab="snow_king_decree_explosion"
                     ParticleEffectSizeModifier="1"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="EnemiesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="50"
                     CrosshairType="SingleTarget"
                     SpellTier="4"
                     BelongsToLoreID="UlricanPrayers"
                     TooltipDescription="{=armor_of_righteousness_description_str}The priest roars with a grand prayer and calls freezing blast of blue flames to cleanse a targeted area of cowards and weaklings. Affected enemies take damage, suffer a -50% movement and attack speed penalty as well as damage over time.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>snow_king_decree</TriggeredEffect>
    </AbilityTemplate>
    <!-- Power of the Lady -->
    <AbilityTemplate StringID="AerialShield"
                     Name="{=aerial_shield_label_str}Aerial Shield"
                     SpriteName="aerial_shield_icon"
                     CoolDown="120"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="45"
                     Radius="8"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="1"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="ethereal_dome"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="AlliesInAOE"
                     CrosshairType="TargetedAOE"
                     MaxDistance="3"
                     TargetCapturingRadius="8"
                     SpellTier="3"
                     BelongsToLoreID="PowerOfLady"
                     TooltipDescription="{=aerial_shield_description_str}Power of the Lady causes enemy missiles to vanish mid-air. Grants 90% ranged physical resistance in a radius.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>aerial_shield</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="AuraOfTheLady"
                     Name="{=aura_of_the_lady_label_str}Aura Of The Lady"
                     SpriteName="auraofthelady_icon"
                     CoolDown="60"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="8"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="AlliesInAOE"
                     CrosshairType="TargetedAOE"
                     MaxDistance="20"
                     TargetCapturingRadius="8"
                     SpellTier="1"
                     BelongsToLoreID="PowerOfLady"
                     TooltipDescription="{=aura_of_the_lady_description_str}The Lady protects Damsels and their followers from harm. Grants 80% magic, lightning and fire resistance in a radius.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>aura_of_lady</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="ShieldOfCombat"
                     Name="{=shield_of_combat_label_str}Shield Of Combat"
                     SpriteName="shieldofcombat_icon"
                     CoolDown="60"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="0"
                     Radius="8"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="-1"
                     TriggerType="TickOnce"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="none"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay="none"
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0"
                     AbilityTargetType="AlliesInAOE"
                     CrosshairType="TargetedAOE"
                     MaxDistance="20"
                     TargetCapturingRadius="8"
                     SpellTier="2"
                     BelongsToLoreID="PowerOfLady"
                     TooltipDescription="{=shield_of_combat_description_str}The Lady's divine power causes allies' armor to deflect enemy blows. Grants 50% physical resistance in a radius.">
        <LightColorRGB x="255"
                       y="255"
                       z="255"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>255</y>
                <z>255</z>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>shield_of_combat</TriggeredEffect>
    </AbilityTemplate>
    <AbilityTemplate StringID="LadysFavour"
                     Name="{=ladys_favour_label_str}Lady's Favour"
                     SpriteName="ladys_favor_icon"
                     CoolDown="60"
                     WindsOfMagicCost="0"
                     BaseMisCastChance="0.05"
                     Duration="10"
                     Radius="6"
                     AbilityType="Prayer"
                     AbilityEffectType="Augment"
                     BaseMovementSpeed="0"
                     TickInterval="2"
                     TriggerType="EveryTick"
                     HasLight="false"
                     LightIntensity="0"
                     LightRadius="0"
                     LightFlickeringMagnitude="0"
                     LightFlickeringInterval="0"
                     ShadowCastEnabled="false"
                     ParticleEffectPrefab="ladys_favour"
                     ParticleEffectSizeModifier="0"
                     SoundEffectToPlay=""
                     ShouldSoundLoopOverDuration="false"
                     CastType="Instant"
                     CastTime="0.5"
                     AnimationActionName="act_release_heavy_thrown"
                     Offset="0.5"
                     AbilityTargetType="AlliesInAOE"
                     TargetCapturingRadius="4"
                     MaxDistance="20"
                     CrosshairType="TargetedAOE"
                     SpellTier="2"
                     BelongsToLoreID="PowerOfLady"
                     TooltipDescription="{=ladys_favour_description_str}The Damsel conjures a vision of the Lady of the Lake to guide Bretonnians into the frey. Grants 25% attack speed bonus in a radius.">
        <LightColorRGB x="255"
                       y="170"
                       z="0"
                       w="-1">
            <AsVec2>
                <x>255</x>
                <y>170</y>
            </AsVec2>
        </LightColorRGB>
        <TriggeredEffect>ladys_favour</TriggeredEffect>
    </AbilityTemplate>
</AbilityTemplates>