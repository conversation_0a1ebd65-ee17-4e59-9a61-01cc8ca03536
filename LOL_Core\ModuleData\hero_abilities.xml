<?xml version="1.0" encoding="utf-8"?>
<HeroAbilities>
  <HeroAbility 
    id="nasus_siphoning_strike" 
    name="{=lol_nasus_q_name}死亡汲取"
    sprite_name="nasus_q_icon"
    base_damage="50"
    can_grow="true"
    growth_per_kill="1"
    required_level="1"
    description="{=lol_nasus_q_desc}内瑟斯挥舞武器进行一次强力攻击，击败敌人永久增加伤害"
    hero_id="nasus"
    skill_type="Active"
    range="2"
    requires_target="true"
    cooldown="8"
    duration="2"
    ability_type="Spell"
    ability_effect_type="CareerAbilityEffect"
    ability_target_type="SingleEnemy"
    cast_type="Instant"
    animation_action_name="act_strike_bent_over"
    sound_effect_to_play="nasus_siphoning_strike"
    has_light="true"
    light_intensity="2"
    light_radius="3"
    light_color_r="138"
    light_color_g="43"
    light_color_b="226"
    particle_effect_prefab="siphoning_strike_effect"
    tooltip_description="{=lol_nasus_q_tooltip}使用此技能击败敌人可永久提升伤害"
  />
</HeroAbilities>
