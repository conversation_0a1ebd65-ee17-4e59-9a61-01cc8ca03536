﻿<Prefab>
	<Constants>
	</Constants>

	<Window>
		<Widget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="132" SuggestedHeight="220" VerticalAlignment="Bottom" HorizontalAlignment="Left" MarginBottom="15" MarginLeft="15" IsVisible="@IsVisible">
			<Children>
				<Widget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="100" SuggestedHeight="100" Sprite="@SpriteName" HorizontalAlignment="Center" VerticalAlignment="Top" PositionYOffset="96">
					<Children>
						<Widget WidthSizePolicy="StretchToParent" HeightSizePolicy="StretchToParent" Sprite="BlankWhiteSquare_9" Color="#000000FF" AlphaFactor="0.6" IsVisible="@IsDisabled"/>
						<TextWidget WidthSizePolicy="StretchToParent" HeightSizePolicy="StretchToParent" HorizontalAlignment="Center" VerticalAlignment="Center" Text="@CoolDownLeft" Brush.FontSize="30" IsVisible="@IsOnCoolDown"/>
						<ListPanel WidthSizePolicy="CoverChildren" HeightSizePolicy="CoverChildren" IsEnabled="false" HorizontalAlignment="Right" VerticalAlignment="Bottom" MarginRight="12" IsVisible="@IsSpell">
							<Children>
								<TextWidget WidthSizePolicy="CoverChildren" HeightSizePolicy="Fixed" SuggestedHeight="33" HorizontalAlignment="Center" VerticalAlignment="Center" Text="@WindsCost" Brush.FontSize="15"/>
								<Widget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="20" SuggestedHeight="20" VerticalAlignment="Center" Sprite="winds_icon_45" MarginLeft="2" />
							</Children>
						</ListPanel>
					</Children>
				</Widget>
				<Widget WidthSizePolicy="StretchToParent" HeightSizePolicy="Fixed" SuggestedHeight="165" Sprite="abilityframe" VerticalAlignment="Top" PositionYOffset="36" IsVisible="true"/>
				<TextWidget WidthSizePolicy="Fixed" HeightSizePolicy="Fixed" SuggestedWidth="25" SuggestedHeight="50" HorizontalAlignment="Center" VerticalAlignment="Top" PositionYOffset="36" Text="@WindsOfMagicLeft" Brush.FontSize="30" Brush.FontColor="#00c5dce3" Brush.TextOutlineAmount="0.1" Brush.TextOutlineColor="#0020aad4"/>
				<TextWidget WidthSizePolicy="StretchToParent" HeightSizePolicy="Fixed" SuggestedHeight="32" HorizontalAlignment="Center" VerticalAlignment="Bottom" Text="@Name" Brush.FontSize="14"/>
			</Children>
		</Widget>
	</Window>
</Prefab>