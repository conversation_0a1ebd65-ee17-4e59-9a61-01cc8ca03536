using System.Collections.Generic;
using System.Linq;
using TaleWorlds.MountAndBlade;
using TaleWorlds.CampaignSystem;
using TaleWorlds.Core;
using TOR_Core.Extensions;
using LOL_Core.Extensions;
using LOL_Core.HeroSkillSystem.Data;

namespace LOL_Core.HeroSkillSystem.Core
{
    public class HeroAbilityComponent : AgentComponent
    {
        private HeroAbility _equippedSkill = null;
        private readonly List<HeroAbility> _availableSkills = new List<HeroAbility>();
        
        public HeroAbility EquippedSkill => _equippedSkill;
        public List<HeroAbility> AvailableSkills => _availableSkills;

        public HeroAbilityComponent(Agent agent) : base(agent)
        {
            InitializeHeroSkills();
        }

        private void InitializeHeroSkills()
        {
            if (Agent.IsMainAgent && Game.Current.GameType is Campaign)
            {
                var hero = Agent.GetHero();
                if (hero != null)
                {
                    LoadHeroSkillData(hero);
                    LoadEquippedSkill(hero);
                }
            }
            else if (Agent.IsMainAgent)
            {
                LoadDefaultSkills();
            }
        }

        private void LoadHeroSkillData(Hero hero)
        {
            var skillData = hero.GetHeroSkillData();
            if (skillData != null)
            {
                foreach (var skillID in skillData.UnlockedSkills)
                {
                    var skill = HeroAbilityFactory.CreateHeroAbility(skillID, Agent);
                    if (skill != null)
                    {
                        var growthData = skillData.GetSkillGrowthData(skillID);
                        if (growthData != null)
                        {
                            skill.LoadGrowthData(growthData);
                        }
                        _availableSkills.Add(skill);
                    }
                }

                if (skillData.UnlockedSkills.Count == 0)
                {
                    UnlockDefaultSkill(hero);
                }
            }
        }

        private void LoadEquippedSkill(Hero hero)
        {
            var equippedSkillID = hero.GetEquippedSkillID();
            if (!string.IsNullOrEmpty(equippedSkillID))
            {
                var skill = _availableSkills.FirstOrDefault(s => s.StringID == equippedSkillID);
                if (skill != null)
                {
                    EquipSkill(skill);
                }
            }
        }

        private void LoadDefaultSkills()
        {
            var nasusSkill = HeroAbilityFactory.CreateHeroAbility("nasus_siphoning_strike", Agent);
            if (nasusSkill != null)
            {
                _availableSkills.Add(nasusSkill);
                EquipSkill(nasusSkill);
            }
        }

        private void UnlockDefaultSkill(Hero hero)
        {
            const string defaultSkillID = "nasus_siphoning_strike";
            hero.UnlockHeroSkill(defaultSkillID);
            
            var skill = HeroAbilityFactory.CreateHeroAbility(defaultSkillID, Agent);
            if (skill != null)
            {
                _availableSkills.Add(skill);
                hero.EquipHeroSkill(defaultSkillID);
                EquipSkill(skill);
            }
        }

        public void EquipSkill(HeroAbility skill)
        {
            if (skill != null && _availableSkills.Contains(skill))
            {
                _equippedSkill = skill;
                
                var hero = Agent.GetHero();
                if (hero != null && Game.Current.GameType is Campaign)
                {
                    hero.EquipHeroSkill(skill.StringID);
                }
            }
        }

        public void UnequipSkill()
        {
            _equippedSkill = null;
            
            var hero = Agent.GetHero();
            if (hero != null && Game.Current.GameType is Campaign)
            {
                hero.UnequipHeroSkill();
            }
        }

        public bool HasEquippedSkill()
        {
            return _equippedSkill != null;
        }

        public HeroAbility GetEquippedSkill()
        {
            return _equippedSkill;
        }

        public HeroAbility GetSkillByID(string skillID)
        {
            return _availableSkills.FirstOrDefault(s => s.StringID == skillID);
        }

        public bool CanUseEquippedSkill()
        {
            if (_equippedSkill == null) return false;
            return _equippedSkill.CanCast(Agent, out _);
        }

        public bool TryUseEquippedSkill()
        {
            if (_equippedSkill == null) return false;
            return _equippedSkill.TryCast(Agent, out _);
        }

        public void SaveSkillData()
        {
            var hero = Agent.GetHero();
            if (hero != null && Game.Current.GameType is Campaign)
            {
                var skillData = hero.GetHeroSkillData();
                if (skillData != null)
                {
                    foreach (var skill in _availableSkills)
                    {
                        var growthData = skill.SaveGrowthData();
                        skillData.UpdateSkillGrowth(growthData);
                    }
                    hero.SetHeroSkillData(skillData);
                }
            }
        }

        public void UnlockSkill(string skillID)
        {
            if (_availableSkills.Any(s => s.StringID == skillID)) return;

            var skill = HeroAbilityFactory.CreateHeroAbility(skillID, Agent);
            if (skill != null)
            {
                _availableSkills.Add(skill);
                
                var hero = Agent.GetHero();
                if (hero != null && Game.Current.GameType is Campaign)
                {
                    hero.UnlockHeroSkill(skillID);
                }
            }
        }

        public void OnSkillKillConfirmed(HeroAbility skill, Agent killer, Agent victim)
        {
            if (skill == _equippedSkill && killer == Agent)
            {
                SaveSkillData();
            }
        }

        public override void OnTickAsAI(float dt)
        {
            base.OnTickAsAI(dt);
            
            if (_equippedSkill != null && _equippedSkill.IsActivationPending)
            {
                _equippedSkill.ActivateAbility(Agent);
            }
        }

        public List<string> GetUnlockedSkillIDs()
        {
            return _availableSkills.Select(s => s.StringID).ToList();
        }

        public int GetSkillStacks(string skillID)
        {
            var skill = GetSkillByID(skillID);
            return skill?.GrowthData.CurrentStacks ?? 0;
        }

        public float GetSkillDamage(string skillID)
        {
            var skill = GetSkillByID(skillID);
            return skill?.GetCurrentDamage() ?? 0f;
        }

        public string GetSkillDescription(string skillID)
        {
            var skill = GetSkillByID(skillID);
            return skill?.GetSkillDescription() ?? "";
        }
    }
}
